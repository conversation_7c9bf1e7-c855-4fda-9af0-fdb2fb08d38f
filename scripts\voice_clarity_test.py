#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音清晰度测试工具
帮助调试和优化小智的语音播报清晰度
"""

import json
import time
from pathlib import Path

def analyze_voice_config():
    """
    分析语音配置文件
    """
    print("🔍 分析语音配置...")
    
    config_file = Path("main/voice_config.h")
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查预设配置
    presets = [
        ("CLEAR_MODE", "清晰模式"),
        ("STANDARD_MODE", "标准模式"), 
        ("FAST_MODE", "快速模式"),
        ("HEALTH_ALERT", "健康提醒"),
        ("EMOTION_CARE", "情绪关怀")
    ]
    
    print("📊 语音预设配置:")
    for preset, desc in presets:
        if preset in content:
            print(f"  ✅ {preset}: {desc}")
        else:
            print(f"  ❌ {preset}: {desc}")
    
    return True

def check_tts_implementation():
    """
    检查TTS实现
    """
    print("\n🔍 检查TTS实现...")
    
    protocol_file = Path("main/protocols/protocol.cc")
    if not protocol_file.exists():
        print(f"❌ 文件不存在: {protocol_file}")
        return False
    
    with open(protocol_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查TTS参数支持
    checks = [
        ("speed参数", "speed"),
        ("pitch参数", "pitch"), 
        ("volume参数", "volume"),
        ("参数化TTS方法", "SendTtsRequest.*float.*float.*float")
    ]
    
    print("📊 TTS参数支持:")
    for check_name, pattern in checks:
        if pattern in content:
            print(f"  ✅ {check_name}")
        else:
            print(f"  ❌ {check_name}")
    
    return True

def generate_test_messages():
    """
    生成测试消息
    """
    print("\n🧪 生成语音测试消息...")
    
    test_messages = [
        {
            "type": "health_posture",
            "text": "我发现你的坐姿不太好，记得挺直腰背哦！",
            "expected_params": "HEALTH_ALERT (慢速、低音调)"
        },
        {
            "type": "emotion_sad", 
            "text": "我注意到你最近看起来有些难过，要不要聊聊天？",
            "expected_params": "EMOTION_CARE (温和语速、温暖音调)"
        },
        {
            "type": "emotion_angry",
            "text": "你看起来有点生气，深呼吸一下，放松心情吧。",
            "expected_params": "EMOTION_CARE (温和语速、温暖音调)"
        },
        {
            "type": "long_message",
            "text": "这是一条比较长的测试消息，用来验证长句子是否会自动使用清晰模式的语音参数配置。",
            "expected_params": "CLEAR_MODE (慢速、低音调)"
        },
        {
            "type": "short_message",
            "text": "短消息测试",
            "expected_params": "STANDARD_MODE (标准语速)"
        }
    ]
    
    print("📝 测试消息列表:")
    for i, msg in enumerate(test_messages, 1):
        print(f"  {i}. {msg['type']}")
        print(f"     文本: {msg['text']}")
        print(f"     预期参数: {msg['expected_params']}")
        print()
    
    return test_messages

def create_voice_test_commands():
    """
    创建语音测试命令
    """
    print("🛠️ 创建语音测试命令...")
    
    commands = [
        {
            "name": "测试清晰模式",
            "description": "使用最慢语速测试",
            "params": {"speed": 0.6, "pitch": 0.8, "volume": 1.0},
            "text": "这是清晰模式测试，语速很慢，音调较低。"
        },
        {
            "name": "测试标准模式", 
            "description": "使用正常语速测试",
            "params": {"speed": 0.8, "pitch": 1.0, "volume": 1.0},
            "text": "这是标准模式测试，语速正常。"
        },
        {
            "name": "测试健康提醒",
            "description": "健康提醒专用参数",
            "params": {"speed": 0.7, "pitch": 0.9, "volume": 1.0},
            "text": "我发现你的坐姿不太好，记得挺直腰背哦！"
        }
    ]
    
    print("🎛️ 语音测试命令:")
    for cmd in commands:
        print(f"  📢 {cmd['name']}")
        print(f"     {cmd['description']}")
        print(f"     参数: 语速={cmd['params']['speed']}, 音调={cmd['params']['pitch']}, 音量={cmd['params']['volume']}")
        print(f"     文本: {cmd['text']}")
        print()
    
    return commands

def generate_optimization_suggestions():
    """
    生成优化建议
    """
    print("💡 语音清晰度优化建议:")
    
    suggestions = [
        {
            "问题": "语速过快，听不清楚",
            "解决方案": [
                "将speed参数从0.8降低到0.6-0.7",
                "使用CLEAR_MODE预设",
                "对长句子自动降低语速"
            ]
        },
        {
            "问题": "音调太高，听起来不自然",
            "解决方案": [
                "将pitch参数从1.0降低到0.8-0.9", 
                "健康提醒使用稍低音调(0.9)",
                "情绪关怀使用温和音调(0.95)"
            ]
        },
        {
            "问题": "音量不够或过大",
            "解决方案": [
                "调整volume参数在0.8-1.0之间",
                "检查硬件音量设置",
                "确保音频编解码器配置正确"
            ]
        },
        {
            "问题": "不同类型消息需要不同语音风格",
            "解决方案": [
                "健康提醒：慢速(0.7)、低音调(0.9)",
                "情绪关怀：温和语速(0.75)、温暖音调(0.95)",
                "紧急警报：标准语速(0.8)、正常音调(1.0)"
            ]
        }
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"\n{i}. {suggestion['问题']}")
        for j, solution in enumerate(suggestion['解决方案'], 1):
            print(f"   {j}) {solution}")

def create_debug_log_guide():
    """
    创建调试日志指南
    """
    print("\n📋 调试日志指南:")
    
    log_patterns = [
        {
            "日志": "🗣️ Sending TTS voice announcement",
            "含义": "开始发送TTS语音播报请求",
            "检查": "确认消息内容是否正确"
        },
        {
            "日志": "🎛️ Using voice params - Speed: X.XX, Pitch: X.XX",
            "含义": "显示使用的语音参数",
            "检查": "确认语速、音调参数是否合理"
        },
        {
            "日志": "✅ TTS started successfully",
            "含义": "TTS开始播放",
            "检查": "如果没有此日志，说明TTS启动失败"
        },
        {
            "日志": "⚠️ TTS not available, using sound backup",
            "含义": "TTS不可用，使用音效备用",
            "检查": "检查网络连接和TTS服务器状态"
        }
    ]
    
    for pattern in log_patterns:
        print(f"  📝 {pattern['日志']}")
        print(f"     含义: {pattern['含义']}")
        print(f"     检查: {pattern['检查']}")
        print()

def main():
    """
    主测试函数
    """
    print("🎤 小智语音清晰度测试工具")
    print("=" * 50)
    
    # 分析配置
    analyze_voice_config()
    
    # 检查实现
    check_tts_implementation()
    
    # 生成测试消息
    test_messages = generate_test_messages()
    
    # 创建测试命令
    test_commands = create_voice_test_commands()
    
    # 生成优化建议
    generate_optimization_suggestions()
    
    # 调试指南
    create_debug_log_guide()
    
    print("\n" + "=" * 50)
    print("🎯 测试步骤:")
    print("1. 编译并烧录修改后的固件")
    print("2. 触发健康提醒功能")
    print("3. 观察串口日志中的语音参数")
    print("4. 评估语音清晰度")
    print("5. 根据需要调整voice_config.h中的参数")
    
    print("\n💡 如果仍然听不清楚:")
    print("- 尝试将speed降低到0.5-0.6")
    print("- 将pitch降低到0.7-0.8") 
    print("- 检查硬件音量和音质")
    print("- 确认TTS服务器支持这些参数")

if __name__ == "__main__":
    main()
