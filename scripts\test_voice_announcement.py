#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语音播报功能的代码修改
验证TTS优先级和音效备用方案
"""

import re
from pathlib import Path

def check_voice_announcement_logic():
    """
    检查语音播报逻辑是否正确实现
    """
    print("🔍 检查语音播报逻辑...")
    
    app_file = Path("main/application.cc")
    if not app_file.exists():
        print(f"❌ 文件不存在: {app_file}")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键逻辑
    checks = [
        {
            "name": "TTS优先级检查",
            "pattern": r"protocol_.*SendTtsRequest.*message",
            "description": "检查是否优先发送TTS请求"
        },
        {
            "name": "TTS状态检查",
            "pattern": r"device_state_.*kDeviceStateSpeaking",
            "description": "检查是否监控TTS播放状态"
        },
        {
            "name": "TTS成功返回",
            "pattern": r"TTS started successfully.*return",
            "description": "检查TTS成功时是否直接返回"
        },
        {
            "name": "音效备用方案",
            "pattern": r"TTS not available.*sound backup",
            "description": "检查TTS失败时的音效备用"
        },
        {
            "name": "音频通道检查",
            "pattern": r"IsAudioChannelOpened",
            "description": "检查是否验证音频通道状态"
        },
        {
            "name": "等待TTS响应",
            "pattern": r"for.*i.*30.*vTaskDelay",
            "description": "检查是否等待TTS响应"
        }
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if re.search(check["pattern"], content, re.DOTALL):
            print(f"✅ {check['name']}: {check['description']}")
            passed += 1
        else:
            print(f"❌ {check['name']}: {check['description']}")
    
    print(f"\n📊 逻辑检查结果: {passed}/{total} 通过")
    return passed == total

def check_message_content():
    """
    检查健康提醒消息内容
    """
    print("\n🔍 检查健康提醒消息内容...")
    
    app_file = Path("main/application.cc")
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查消息内容
    messages = [
        {
            "type": "姿势提醒",
            "pattern": r"我发现你的坐姿不太好",
            "description": "坐姿不良提醒消息"
        },
        {
            "type": "情绪关怀-悲伤",
            "pattern": r"我注意到你最近看起来有些难过",
            "description": "悲伤情绪关怀消息"
        },
        {
            "type": "情绪关怀-愤怒",
            "pattern": r"你看起来有点生气",
            "description": "愤怒情绪关怀消息"
        },
        {
            "type": "情绪关怀-开心",
            "pattern": r"你看起来心情很好",
            "description": "开心情绪确认消息"
        }
    ]
    
    passed = 0
    total = len(messages)
    
    for msg in messages:
        if re.search(msg["pattern"], content):
            print(f"✅ {msg['type']}: {msg['description']}")
            passed += 1
        else:
            print(f"❌ {msg['type']}: {msg['description']}")
    
    print(f"\n📊 消息内容检查: {passed}/{total} 通过")
    return passed == total

def check_fallback_sounds():
    """
    检查音效备用方案
    """
    print("\n🔍 检查音效备用方案...")
    
    app_file = Path("main/application.cc")
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否使用现有音效作为备用
    fallback_checks = [
        {
            "name": "备用音效使用",
            "pattern": r"P3_ACTIVATION.*临时使用现有音效",
            "description": "检查是否使用现有音效作为备用"
        },
        {
            "name": "音效播放调用",
            "pattern": r"PlaySound.*alert_sound",
            "description": "检查音效播放调用"
        },
        {
            "name": "立即播放调用",
            "pattern": r"PlaySoundImmediate",
            "description": "检查立即播放音效调用"
        }
    ]
    
    passed = 0
    total = len(fallback_checks)
    
    for check in fallback_checks:
        if re.search(check["pattern"], content):
            print(f"✅ {check['name']}: {check['description']}")
            passed += 1
        else:
            print(f"❌ {check['name']}: {check['description']}")
    
    print(f"\n📊 备用方案检查: {passed}/{total} 通过")
    return passed == total

def check_code_structure():
    """
    检查代码结构是否合理
    """
    print("\n🔍 检查代码结构...")
    
    app_file = Path("main/application.cc")
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找健康警报函数
    health_alert_pattern = r'Schedule\(\[this, message, emotion, type, state\]\(\) \{(.*?)\}\);'
    matches = re.findall(health_alert_pattern, content, re.DOTALL)
    
    if not matches:
        print("❌ 未找到健康警报处理函数")
        return False
    
    alert_code = matches[0]
    
    # 检查代码结构
    structure_checks = [
        {
            "name": "TTS优先处理",
            "pattern": r"protocol_.*SendTtsRequest.*return",
            "description": "TTS成功时直接返回"
        },
        {
            "name": "条件判断",
            "pattern": r"if.*tts_success.*else",
            "description": "TTS成功/失败的条件判断"
        },
        {
            "name": "错误处理",
            "pattern": r"TTS not available",
            "description": "TTS不可用时的错误处理"
        }
    ]
    
    passed = 0
    total = len(structure_checks)
    
    for check in structure_checks:
        if re.search(check["pattern"], alert_code):
            print(f"✅ {check['name']}: {check['description']}")
            passed += 1
        else:
            print(f"❌ {check['name']}: {check['description']}")
    
    print(f"\n📊 代码结构检查: {passed}/{total} 通过")
    return passed == total

def main():
    """
    主测试函数
    """
    print("🧪 语音播报功能测试")
    print("=" * 50)
    
    tests = [
        ("语音播报逻辑", check_voice_announcement_logic),
        ("消息内容检查", check_message_content),
        ("音效备用方案", check_fallback_sounds),
        ("代码结构检查", check_code_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！语音播报功能已正确实现。")
        print("\n📝 功能说明:")
        print("1. ✅ 优先使用TTS语音播报")
        print("2. ✅ TTS不可用时使用音效备用")
        print("3. ✅ 智能判断播放状态")
        print("4. ✅ 完整的错误处理")
        print("\n🚀 下一步:")
        print("1. 编译项目")
        print("2. 烧录到设备")
        print("3. 测试语音播报功能")
        print("4. 验证TTS和音效备用方案")
    else:
        print("⚠️  部分测试失败，请检查代码。")
    
    return passed == total

if __name__ == "__main__":
    main()
