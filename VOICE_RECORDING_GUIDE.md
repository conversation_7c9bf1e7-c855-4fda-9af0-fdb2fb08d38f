# 小智健康提醒语音录制脚本

## 录制要求
- 采样率: 16kHz
- 声道: 单声道 (Mono)
- 格式: MP3
- 比特率: 32-64 kbps
- 语速: 根据优先级调整

## 录制建议
- 🔴 高优先级: 慢速(0.7x)、清晰、稍低音调，确保用户能听清楚
- 🟡 普通优先级: 正常语速(1.0x)、温和音调
- 🟢 低优先级: 轻柔语速(0.8x)、温暖音调

## 录制内容

### 1. 坐姿不良提醒 🔴
**文件名**: `health_posture_bad.mp3`
**内容**: "我发现你的坐姿不太好，记得挺直腰背哦！"
**优先级**: high

### 2. 坐姿良好确认 🟡
**文件名**: `health_posture_good.mp3`
**内容**: "你的坐姿很端正，继续保持！"
**优先级**: normal

### 3. 悲伤情绪关怀 🔴
**文件名**: `health_emotion_sad.mp3`
**内容**: "我注意到你最近看起来有些难过，要不要聊聊天？"
**优先级**: high

### 4. 愤怒情绪安抚 🔴
**文件名**: `health_emotion_angry.mp3`
**内容**: "你看起来有点生气，深呼吸一下，放松心情吧。"
**优先级**: high

### 5. 开心情绪确认 🟡
**文件名**: `health_emotion_happy.mp3`
**内容**: "你看起来心情很好呢，真棒！"
**优先级**: normal

### 6. 休息提醒 🟡
**文件名**: `health_break_reminder.mp3`
**内容**: "你已经工作很久了，该休息一下了。"
**优先级**: normal

### 7. 眼部护理提醒 🟡
**文件名**: `health_eye_care.mp3`
**内容**: "记得让眼睛休息一下，看看远处的风景吧。"
**优先级**: normal

### 8. 温和健康提醒 🟢
**文件名**: `health_gentle_reminder.mp3`
**内容**: "小智提醒您注意健康哦。"
**优先级**: low

### 9. 健康成功确认 🟡
**文件名**: `health_success_confirm.mp3`
**内容**: "很好，继续保持健康的生活习惯！"
**优先级**: normal

### 10. 健康警报开始音 🔴
**文件名**: `health_alert_start.mp3`
**内容**: "健康提醒"
**优先级**: high


## 录制后处理
1. 将所有MP3文件放在 `temp_voice/` 目录
2. 运行转换脚本: `python scripts/convert_voice_to_p3.py`
3. 重新编译项目: `idf.py build`
4. 烧录测试: `idf.py flash`

## 质量检查
- 每个文件大小应在 1-10KB 之间
- 语音清晰，无杂音
- 语速适中，易于理解
- 音调自然，符合场景
