#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建健康提醒音效
生成不同类型的提示音，然后转换为P3格式
"""

import os
import sys
import subprocess
import numpy as np
import wave
from pathlib import Path

def create_tone(frequency, duration, sample_rate=16000, fade_duration=0.1):
    """
    创建单一频率的音调
    """
    samples = int(sample_rate * duration)
    t = np.linspace(0, duration, samples, False)
    
    # 生成正弦波
    wave_data = np.sin(2 * np.pi * frequency * t)
    
    # 添加淡入淡出效果
    fade_samples = int(sample_rate * fade_duration)
    if fade_samples > 0:
        wave_data[:fade_samples] *= np.linspace(0, 1, fade_samples)
        wave_data[-fade_samples:] *= np.linspace(1, 0, fade_samples)
    
    return wave_data

def create_chord(frequencies, duration, sample_rate=16000):
    """
    创建和弦音效
    """
    wave_data = np.zeros(int(sample_rate * duration))
    
    for freq in frequencies:
        tone = create_tone(freq, duration, sample_rate, fade_duration=0.05)
        wave_data += tone / len(frequencies)
    
    return wave_data

def create_melody(notes, note_duration=0.3, sample_rate=16000):
    """
    创建简单旋律
    notes: 频率列表
    """
    wave_data = np.array([])
    
    for freq in notes:
        if freq == 0:  # 休止符
            silence = np.zeros(int(sample_rate * note_duration))
            wave_data = np.concatenate([wave_data, silence])
        else:
            tone = create_tone(freq, note_duration, sample_rate, fade_duration=0.05)
            wave_data = np.concatenate([wave_data, tone])
    
    return wave_data

def create_notification_sound(sound_type="gentle"):
    """
    创建不同类型的通知音效
    """
    if sound_type == "gentle":
        # 温和的提醒音 - 上升音调
        frequencies = [523, 659, 784]  # C5, E5, G5
        return create_melody(frequencies, note_duration=0.4)
    
    elif sound_type == "attention":
        # 注意音 - 双音调
        frequencies = [880, 0, 880]  # A5, 休止, A5
        return create_melody(frequencies, note_duration=0.3)
    
    elif sound_type == "warning":
        # 警告音 - 低频重复
        frequencies = [440, 0, 440, 0, 440]  # A4 重复
        return create_melody(frequencies, note_duration=0.2)
    
    elif sound_type == "success":
        # 成功音 - 上升三和弦
        frequencies = [523, 659, 784, 1047]  # C5, E5, G5, C6
        return create_melody(frequencies, note_duration=0.25)
    
    elif sound_type == "calm":
        # 平静音 - 和弦
        frequencies = [261, 329, 392]  # C4, E4, G4
        return create_chord(frequencies, duration=1.5)
    
    else:
        # 默认简单提示音
        return create_tone(800, 1.0)

def save_wave_file(wave_data, filename, sample_rate=16000):
    """
    保存波形数据为WAV文件
    """
    # 标准化并转换为16位整数
    wave_data = wave_data / np.max(np.abs(wave_data))  # 标准化
    wave_data = (wave_data * 32767 * 0.8).astype(np.int16)  # 转换为16位，留一些余量
    
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(wave_data.tobytes())

def convert_to_p3(wav_file, p3_file):
    """
    将WAV文件转换为P3格式
    """
    try:
        script_dir = Path(__file__).parent
        convert_script = script_dir / "p3_tools" / "convert_audio_to_p3.py"
        
        if not convert_script.exists():
            print(f"错误: 转换脚本不存在: {convert_script}")
            return False
        
        cmd = [
            sys.executable,
            str(convert_script),
            str(wav_file),
            str(p3_file),
            "-d"  # 禁用响度标准化
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
        
    except Exception as e:
        print(f"P3转换错误: {e}")
        return False

def main():
    """
    主函数：创建健康提醒音效
    """
    # 定义要创建的音效
    sound_definitions = {
        "health_gentle": ("gentle", "温和的健康提醒音"),
        "health_attention": ("attention", "需要注意的健康提醒"),
        "health_warning": ("warning", "健康警告音"),
        "health_success": ("success", "健康目标达成音"),
        "health_calm": ("calm", "放松提醒音"),
        "posture_reminder": ("gentle", "姿势提醒音"),
        "break_reminder": ("attention", "休息提醒音"),
        "eye_care": ("calm", "眼部护理提醒音"),
    }
    
    # 创建输出目录
    output_dir = Path("health_tones")
    wav_dir = output_dir / "wav"
    p3_dir = output_dir / "p3"
    
    wav_dir.mkdir(parents=True, exist_ok=True)
    p3_dir.mkdir(parents=True, exist_ok=True)
    
    print("开始创建健康提醒音效...")
    print(f"输出目录: {output_dir.absolute()}")
    
    success_count = 0
    total_count = len(sound_definitions)
    
    for filename, (sound_type, description) in sound_definitions.items():
        wav_file = wav_dir / f"{filename}.wav"
        p3_file = p3_dir / f"{filename}.p3"
        
        print(f"\n创建: {filename} - {description}")
        
        try:
            # 生成音效
            wave_data = create_notification_sound(sound_type)
            
            # 保存WAV文件
            save_wave_file(wave_data, str(wav_file))
            print(f"✅ WAV文件已保存: {wav_file}")
            
            # 转换为P3格式
            if convert_to_p3(wav_file, p3_file):
                success_count += 1
                print(f"✅ P3文件已生成: {p3_file}")
            else:
                print(f"❌ P3转换失败: {filename}")
                
        except Exception as e:
            print(f"❌ 音效生成失败: {filename} - {e}")
    
    print(f"\n生成完成！成功: {success_count}/{total_count}")
    print(f"WAV文件位置: {wav_dir.absolute()}")
    print(f"P3文件位置: {p3_dir.absolute()}")
    
    if success_count > 0:
        print("\n下一步:")
        print("1. 将P3文件复制到 main/assets/zh-CN/ 目录")
        print("2. 运行 python scripts/gen_lang.py 更新语言配置")
        print("3. 在代码中使用新的音效常量")
        print("\n可用的音效常量:")
        for filename in sound_definitions.keys():
            constant_name = f"P3_{filename.upper()}"
            print(f"  Lang::Sounds::{constant_name}")

if __name__ == "__main__":
    try:
        import numpy as np
        main()
    except ImportError:
        print("错误: 需要安装numpy")
        print("请运行: pip install numpy")
        sys.exit(1)
