#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试健康提醒音效文件
验证音效文件是否正确创建和配置
"""

import os
from pathlib import Path

def test_sound_files():
    """
    测试音效文件是否存在
    """
    print("🔍 检查健康提醒音效文件...")
    
    # 检查assets目录中的音效文件
    assets_dir = Path("main/assets/zh-CN")
    
    expected_files = [
        "health_gentle.p3",
        "health_attention.p3", 
        "health_warning.p3",
        "health_success.p3",
        "posture_reminder.p3",
        "break_reminder.p3",
        "eye_care.p3",
        "emotion_care.p3"
    ]
    
    print(f"检查目录: {assets_dir.absolute()}")
    
    missing_files = []
    existing_files = []
    
    for filename in expected_files:
        file_path = assets_dir / filename
        if file_path.exists():
            file_size = file_path.stat().st_size
            existing_files.append((filename, file_size))
            print(f"✅ {filename} - {file_size} bytes")
        else:
            missing_files.append(filename)
            print(f"❌ {filename} - 文件不存在")
    
    print(f"\n📊 统计:")
    print(f"存在的文件: {len(existing_files)}/{len(expected_files)}")
    print(f"缺失的文件: {len(missing_files)}")
    
    if missing_files:
        print(f"\n❌ 缺失的文件:")
        for filename in missing_files:
            print(f"  - {filename}")
    
    return len(missing_files) == 0

def test_language_config():
    """
    测试语言配置文件是否包含新的音效常量
    """
    print("\n🔍 检查语言配置文件...")
    
    config_file = Path("main/assets/lang_config.h")
    
    if not config_file.exists():
        print(f"❌ 语言配置文件不存在: {config_file}")
        return False
    
    expected_constants = [
        "P3_HEALTH_GENTLE",
        "P3_HEALTH_ATTENTION",
        "P3_HEALTH_WARNING", 
        "P3_HEALTH_SUCCESS",
        "P3_POSTURE_REMINDER",
        "P3_BREAK_REMINDER",
        "P3_EYE_CARE",
        "P3_EMOTION_CARE"
    ]
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    missing_constants = []
    existing_constants = []
    
    for constant in expected_constants:
        if constant in content:
            existing_constants.append(constant)
            print(f"✅ {constant}")
        else:
            missing_constants.append(constant)
            print(f"❌ {constant} - 常量不存在")
    
    print(f"\n📊 统计:")
    print(f"存在的常量: {len(existing_constants)}/{len(expected_constants)}")
    print(f"缺失的常量: {len(missing_constants)}")
    
    if missing_constants:
        print(f"\n❌ 缺失的常量:")
        for constant in missing_constants:
            print(f"  - {constant}")
    
    return len(missing_constants) == 0

def test_application_code():
    """
    测试应用代码是否正确使用新的音效常量
    """
    print("\n🔍 检查应用代码...")
    
    app_file = Path("main/application.cc")
    
    if not app_file.exists():
        print(f"❌ 应用文件不存在: {app_file}")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否使用了新的音效常量
    health_constants = [
        "P3_POSTURE_REMINDER",
        "P3_EMOTION_CARE", 
        "P3_HEALTH_ATTENTION",
        "P3_HEALTH_GENTLE",
        "P3_HEALTH_SUCCESS"
    ]
    
    used_constants = []
    unused_constants = []
    
    for constant in health_constants:
        if f"Lang::Sounds::{constant}" in content:
            used_constants.append(constant)
            print(f"✅ {constant} - 已在代码中使用")
        else:
            unused_constants.append(constant)
            print(f"❌ {constant} - 未在代码中使用")
    
    print(f"\n📊 统计:")
    print(f"已使用的常量: {len(used_constants)}/{len(health_constants)}")
    print(f"未使用的常量: {len(unused_constants)}")
    
    return len(unused_constants) == 0

def main():
    """
    主测试函数
    """
    print("🧪 健康提醒音效测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("音效文件测试", test_sound_files),
        ("语言配置测试", test_language_config),
        ("应用代码测试", test_application_code)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！健康提醒音效配置正确。")
        print("\n📝 下一步:")
        print("1. 重新编译项目")
        print("2. 烧录到设备")
        print("3. 测试健康提醒功能")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    
    return passed == total

if __name__ == "__main__":
    main()
