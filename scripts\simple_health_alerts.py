#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版健康提醒语音生成脚本
使用pyttsx3生成语音，然后转换为P3格式
"""

import os
import sys
import subprocess
from pathlib import Path
import time

# 健康提醒话语（精选版）
HEALTH_ALERTS = {
    # 姿势提醒
    "posture_reminder": "请注意您的坐姿，保持背部挺直",
    "posture_warning": "检测到不良坐姿，请调整您的姿势",
    
    # 休息提醒
    "break_reminder": "您已经工作很久了，建议休息一下",
    "activity_reminder": "该起来活动活动了，适当休息有益健康",
    
    # 眼部护理
    "eye_care": "请注意保护眼睛，适当远眺放松",
    
    # 情绪关怀
    "emotion_care": "检测到您可能有些疲惫，请注意休息",
    
    # 通用健康提醒
    "health_reminder": "小智提醒您：健康工作，快乐生活",
    
    # 鼓励话语
    "encouragement": "您今天的坐姿很标准，继续保持",
}

def generate_tts_with_pyttsx3(text, output_file):
    """
    使用pyttsx3生成语音文件
    """
    try:
        import pyttsx3
        
        # 初始化TTS引擎
        engine = pyttsx3.init()
        
        # 设置语音属性
        voices = engine.getProperty('voices')
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                engine.setProperty('voice', voice.id)
                break
        
        # 设置语速和音量
        engine.setProperty('rate', 150)  # 语速
        engine.setProperty('volume', 0.9)  # 音量
        
        # 保存为WAV文件
        wav_file = output_file.replace('.mp3', '.wav')
        engine.save_to_file(text, wav_file)
        engine.runAndWait()
        
        # 转换WAV为MP3（如果需要）
        if output_file.endswith('.mp3'):
            try:
                # 使用ffmpeg转换
                cmd = ['ffmpeg', '-i', wav_file, '-y', output_file]
                subprocess.run(cmd, capture_output=True)
                os.remove(wav_file)  # 删除临时WAV文件
            except:
                # 如果ffmpeg不可用，直接使用WAV文件
                os.rename(wav_file, output_file.replace('.mp3', '.wav'))
                return output_file.replace('.mp3', '.wav')
        
        return output_file
        
    except ImportError:
        print("错误: 未安装pyttsx3，请运行: pip install pyttsx3")
        return None
    except Exception as e:
        print(f"TTS生成错误: {e}")
        return None

def generate_simple_audio(text, output_file):
    """
    生成简单的音频文件（使用系统TTS或创建静音文件）
    """
    try:
        # 尝试使用Windows SAPI
        if os.name == 'nt':
            import win32com.client
            speaker = win32com.client.Dispatch("SAPI.SpVoice")
            
            # 设置中文语音
            voices = speaker.GetVoices()
            for voice in voices:
                if 'chinese' in voice.GetDescription().lower():
                    speaker.Voice = voice
                    break
            
            # 保存为WAV文件
            wav_file = output_file.replace('.mp3', '.wav')
            file_stream = win32com.client.Dispatch("SAPI.SpFileStream")
            file_stream.Open(wav_file, 3)
            speaker.AudioOutputStream = file_stream
            speaker.Speak(text)
            file_stream.Close()
            
            return wav_file
            
    except ImportError:
        pass
    except Exception as e:
        print(f"系统TTS错误: {e}")
    
    # 如果系统TTS不可用，创建一个简单的提示音
    return create_simple_tone(output_file, duration=2.0)

def create_simple_tone(output_file, duration=2.0, frequency=800):
    """
    创建简单的提示音
    """
    try:
        import numpy as np
        import wave
        
        sample_rate = 16000
        samples = int(sample_rate * duration)
        
        # 生成正弦波
        t = np.linspace(0, duration, samples, False)
        wave_data = np.sin(2 * np.pi * frequency * t)
        
        # 添加淡入淡出效果
        fade_samples = int(sample_rate * 0.1)  # 0.1秒淡入淡出
        wave_data[:fade_samples] *= np.linspace(0, 1, fade_samples)
        wave_data[-fade_samples:] *= np.linspace(1, 0, fade_samples)
        
        # 转换为16位整数
        wave_data = (wave_data * 32767).astype(np.int16)
        
        # 保存为WAV文件
        wav_file = output_file.replace('.mp3', '.wav')
        with wave.open(wav_file, 'w') as wav_f:
            wav_f.setnchannels(1)  # 单声道
            wav_f.setsampwidth(2)  # 16位
            wav_f.setframerate(sample_rate)
            wav_f.writeframes(wave_data.tobytes())
        
        print(f"创建提示音: {wav_file}")
        return wav_file
        
    except ImportError:
        print("错误: 未安装numpy，无法生成提示音")
        return None
    except Exception as e:
        print(f"提示音生成错误: {e}")
        return None

def convert_audio_to_p3(audio_file, p3_file):
    """
    将音频文件转换为P3格式
    """
    try:
        # 获取脚本目录
        script_dir = Path(__file__).parent
        convert_script = script_dir / "p3_tools" / "convert_audio_to_p3.py"
        
        if not convert_script.exists():
            print(f"错误: 转换脚本不存在: {convert_script}")
            return False
        
        cmd = [
            sys.executable,
            str(convert_script),
            str(audio_file),
            str(p3_file),
            "-d"  # 禁用响度标准化
        ]
        
        print(f"转换为P3格式: {audio_file} -> {p3_file}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"P3转换失败: {result.stderr}")
            return False
            
        return True
        
    except Exception as e:
        print(f"P3转换错误: {e}")
        return False

def main():
    """
    主函数：生成健康提醒语音文件
    """
    # 创建输出目录
    output_dir = Path("health_alerts_simple")
    audio_dir = output_dir / "audio"
    p3_dir = output_dir / "p3"
    
    audio_dir.mkdir(parents=True, exist_ok=True)
    p3_dir.mkdir(parents=True, exist_ok=True)
    
    print("开始生成健康提醒语音文件...")
    print(f"输出目录: {output_dir.absolute()}")
    
    success_count = 0
    total_count = len(HEALTH_ALERTS)
    
    for filename, text in HEALTH_ALERTS.items():
        audio_file = audio_dir / f"{filename}.wav"
        p3_file = p3_dir / f"{filename}.p3"
        
        print(f"\n处理: {filename} - {text}")
        
        # 生成音频文件
        generated_file = generate_tts_with_pyttsx3(text, str(audio_file))
        if not generated_file:
            generated_file = generate_simple_audio(text, str(audio_file))
        
        if generated_file and os.path.exists(generated_file):
            # 转换为P3格式
            if convert_audio_to_p3(generated_file, p3_file):
                success_count += 1
                print(f"✅ 成功生成: {filename}")
            else:
                print(f"❌ P3转换失败: {filename}")
        else:
            print(f"❌ 音频生成失败: {filename}")
    
    print(f"\n生成完成！成功: {success_count}/{total_count}")
    print(f"音频文件位置: {audio_dir.absolute()}")
    print(f"P3文件位置: {p3_dir.absolute()}")
    
    if success_count > 0:
        print("\n下一步:")
        print("1. 将P3文件复制到 main/assets/zh-CN/ 目录")
        print("2. 运行 python scripts/gen_lang.py 更新语言配置")
        print("3. 重新编译项目")

if __name__ == "__main__":
    main()
