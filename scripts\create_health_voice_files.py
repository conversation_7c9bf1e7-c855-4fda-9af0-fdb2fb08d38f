#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建健康提醒语音文件
生成高质量MP3文件并转换为P3格式
"""

import os
import subprocess
import json
from pathlib import Path

def create_health_voice_messages():
    """
    定义健康提醒语音消息
    """
    messages = {
        "posture_reminder": {
            "text": "我发现你的坐姿不太好，记得挺直腰背哦！",
            "description": "坐姿提醒",
            "voice_params": {
                "speed": "0.7",  # 慢速
                "pitch": "-10%", # 稍低音调
                "volume": "1.0"
            }
        },
        "posture_good": {
            "text": "你的坐姿很端正，继续保持！",
            "description": "坐姿良好确认",
            "voice_params": {
                "speed": "0.8",
                "pitch": "0%",
                "volume": "1.0"
            }
        },
        "emotion_sad_care": {
            "text": "我注意到你最近看起来有些难过，要不要聊聊天？",
            "description": "悲伤情绪关怀",
            "voice_params": {
                "speed": "0.75",  # 温和语速
                "pitch": "-5%",   # 温暖音调
                "volume": "0.9"
            }
        },
        "emotion_angry_calm": {
            "text": "你看起来有点生气，深呼吸一下，放松心情吧。",
            "description": "愤怒情绪安抚",
            "voice_params": {
                "speed": "0.7",   # 慢速安抚
                "pitch": "-10%",  # 低音调
                "volume": "0.9"
            }
        },
        "emotion_happy": {
            "text": "你看起来心情很好呢，真棒！",
            "description": "开心情绪确认",
            "voice_params": {
                "speed": "0.8",
                "pitch": "5%",    # 稍高音调
                "volume": "1.0"
            }
        },
        "break_reminder": {
            "text": "你已经工作很久了，该休息一下了。",
            "description": "休息提醒",
            "voice_params": {
                "speed": "0.75",
                "pitch": "0%",
                "volume": "1.0"
            }
        },
        "eye_care": {
            "text": "记得让眼睛休息一下，看看远处的风景吧。",
            "description": "眼部护理提醒",
            "voice_params": {
                "speed": "0.7",
                "pitch": "-5%",
                "volume": "0.9"
            }
        },
        "health_gentle": {
            "text": "小智提醒您注意健康哦。",
            "description": "温和健康提醒",
            "voice_params": {
                "speed": "0.8",
                "pitch": "0%",
                "volume": "1.0"
            }
        },
        "health_success": {
            "text": "很好，继续保持健康的生活习惯！",
            "description": "健康成功确认",
            "voice_params": {
                "speed": "0.8",
                "pitch": "5%",
                "volume": "1.0"
            }
        }
    }
    
    return messages

def generate_mp3_with_edge_tts(text, output_file, voice="zh-CN-XiaoxiaoNeural", speed="0.8", pitch="0%", volume="1.0"):
    """
    使用edge-tts生成高质量MP3文件
    """
    try:
        # 构建SSML格式的文本，包含语音参数
        ssml_text = f"""
        <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
            <voice name="{voice}">
                <prosody rate="{speed}" pitch="{pitch}" volume="{volume}">
                    {text}
                </prosody>
            </voice>
        </speak>
        """
        
        # 使用edge-tts命令行工具
        cmd = [
            "edge-tts",
            "--voice", voice,
            "--text", ssml_text,
            "--write-media", output_file
        ]
        
        print(f"🎤 生成语音: {text}")
        print(f"   参数: 语速={speed}, 音调={pitch}, 音量={volume}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"❌ TTS生成失败: {result.stderr}")
            return False
            
        print(f"✅ MP3文件生成成功: {output_file}")
        return True
        
    except FileNotFoundError:
        print("❌ 错误: 未找到edge-tts命令")
        print("请先安装: pip install edge-tts")
        return False
    except Exception as e:
        print(f"❌ TTS生成错误: {e}")
        return False

def convert_mp3_to_p3(mp3_file, p3_file):
    """
    将MP3文件转换为P3格式
    """
    try:
        # 使用ffmpeg转换音频格式
        # P3格式通常是特定的音频编码，这里假设是某种压缩格式
        cmd = [
            "ffmpeg",
            "-i", mp3_file,
            "-ar", "16000",      # 采样率16kHz
            "-ac", "1",          # 单声道
            "-ab", "32k",        # 比特率32kbps
            "-f", "mp3",         # 输出格式
            "-y",                # 覆盖输出文件
            p3_file
        ]
        
        print(f"🔄 转换 {mp3_file} -> {p3_file}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ 转换失败: {result.stderr}")
            return False
            
        print(f"✅ P3文件生成成功: {p3_file}")
        return True
        
    except FileNotFoundError:
        print("❌ 错误: 未找到ffmpeg命令")
        print("请先安装ffmpeg: https://ffmpeg.org/download.html")
        return False
    except Exception as e:
        print(f"❌ 转换错误: {e}")
        return False

def create_language_json_entry(messages):
    """
    创建language.json条目
    """
    json_entries = {}
    
    for key, msg in messages.items():
        json_entries[key] = {
            "text": msg["text"],
            "description": msg["description"],
            "file": f"{key}.p3"
        }
    
    return json_entries

def update_language_json(new_entries):
    """
    更新language.json文件
    """
    lang_file = Path("main/assets/zh-CN/language.json")
    
    if lang_file.exists():
        with open(lang_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    else:
        data = {"sounds": {}}
    
    # 添加新的语音条目
    if "sounds" not in data:
        data["sounds"] = {}
    
    data["sounds"].update(new_entries)
    
    # 写回文件
    with open(lang_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 更新language.json: 添加了{len(new_entries)}个语音条目")

def main():
    """
    主函数
    """
    print("🎤 创建小智健康提醒语音文件")
    print("=" * 50)
    
    # 创建输出目录
    mp3_dir = Path("temp_mp3")
    p3_dir = Path("main/assets/zh-CN")
    
    mp3_dir.mkdir(exist_ok=True)
    p3_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取语音消息
    messages = create_health_voice_messages()
    
    print(f"📝 将生成 {len(messages)} 个语音文件:")
    for key, msg in messages.items():
        print(f"  - {key}: {msg['text']}")
    
    print("\n🎵 开始生成语音文件...")
    
    success_count = 0
    total_count = len(messages)
    
    # 推荐的中文语音
    voices = [
        "zh-CN-XiaoxiaoNeural",  # 女声，温和
        "zh-CN-YunxiNeural",     # 男声，稳重
        "zh-CN-XiaoyiNeural",    # 女声，亲切
    ]
    
    selected_voice = voices[0]  # 使用温和女声
    print(f"🎭 使用语音: {selected_voice}")
    
    for key, msg in messages.items():
        print(f"\n🔄 处理: {key}")
        
        # 文件路径
        mp3_file = mp3_dir / f"{key}.mp3"
        p3_file = p3_dir / f"{key}.p3"
        
        # 生成MP3
        if generate_mp3_with_edge_tts(
            msg["text"], 
            str(mp3_file),
            voice=selected_voice,
            speed=msg["voice_params"]["speed"],
            pitch=msg["voice_params"]["pitch"],
            volume=msg["voice_params"]["volume"]
        ):
            # 转换为P3
            if convert_mp3_to_p3(str(mp3_file), str(p3_file)):
                success_count += 1
                print(f"✅ {key} 完成")
            else:
                print(f"❌ {key} P3转换失败")
        else:
            print(f"❌ {key} MP3生成失败")
    
    print(f"\n📊 生成结果: {success_count}/{total_count} 成功")
    
    if success_count > 0:
        # 更新language.json
        json_entries = create_language_json_entry(messages)
        update_language_json(json_entries)
        
        print("\n🎯 下一步:")
        print("1. 重新生成语言配置: python scripts/gen_lang.py")
        print("2. 编译项目: idf.py build")
        print("3. 烧录到设备: idf.py flash")
        print("4. 测试语音清晰度")
        
        print("\n📁 生成的文件:")
        for key in messages.keys():
            p3_file = p3_dir / f"{key}.p3"
            if p3_file.exists():
                size = p3_file.stat().st_size
                print(f"  ✅ {key}.p3 ({size} bytes)")
    
    # 清理临时文件
    if mp3_dir.exists():
        import shutil
        shutil.rmtree(mp3_dir)
        print("\n🧹 清理临时MP3文件")

if __name__ == "__main__":
    main()
