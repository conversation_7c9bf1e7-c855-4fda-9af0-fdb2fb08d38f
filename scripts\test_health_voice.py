#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试健康语音文件
"""

from pathlib import Path

def test_health_voice_files():
    """
    测试健康语音文件是否正确创建
    """
    print("🧪 测试健康语音文件...")
    
    target_dir = Path("main/assets/zh-CN")
    
    test_files = {
        "health_posture_bad.p3": "坐姿不良提醒",
        "health_posture_good.p3": "坐姿良好确认",
        "health_emotion_sad.p3": "悲伤情绪关怀", 
        "health_emotion_angry.p3": "愤怒情绪安抚",
        "health_emotion_happy.p3": "开心情绪确认",
        "health_gentle_reminder.p3": "温和健康提醒",
        "health_success_confirm.p3": "健康成功确认"
    }
    
    success_count = 0
    for filename, description in test_files.items():
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            if size > 100:  # 至少100字节
                print(f"✅ {filename}: {description} ({size} bytes)")
                success_count += 1
            else:
                print(f"⚠️ {filename}: {description} (文件太小: {size} bytes)")
        else:
            print(f"❌ {filename}: {description} (不存在)")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_files)} 通过")
    
    if success_count == len(test_files):
        print("🎉 所有健康语音文件测试通过！")
        print("\n🚀 下一步:")
        print("1. 编译项目: idf.py build")
        print("2. 烧录设备: idf.py flash") 
        print("3. 测试健康警报功能")
        print("4. 观察是否播放正确的音效")
        return True
    else:
        print("❌ 部分文件测试失败")
        return False

if __name__ == "__main__":
    test_health_voice_files()
