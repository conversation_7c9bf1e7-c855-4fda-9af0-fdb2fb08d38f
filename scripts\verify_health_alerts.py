#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证健康提醒功能的代码修改
检查代码语法和逻辑是否正确
"""

import re
from pathlib import Path

def check_application_code():
    """
    检查application.cc中的健康提醒代码
    """
    print("🔍 检查 application.cc 中的健康提醒代码...")
    
    app_file = Path("main/application.cc")
    if not app_file.exists():
        print(f"❌ 文件不存在: {app_file}")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键代码片段
    checks = [
        {
            "name": "Lambda捕获列表",
            "pattern": r"Schedule\(\[this, message, emotion, type, state\]",
            "description": "检查lambda函数是否正确捕获所需变量"
        },
        {
            "name": "姿势警报音效",
            "pattern": r"Lang::Sounds::P3_POSTURE_REMINDER",
            "description": "检查是否使用姿势提醒音效"
        },
        {
            "name": "情绪关怀音效",
            "pattern": r"Lang::Sounds::P3_EMOTION_CARE",
            "description": "检查是否使用情绪关怀音效"
        },
        {
            "name": "健康注意音效",
            "pattern": r"Lang::Sounds::P3_HEALTH_ATTENTION",
            "description": "检查是否使用健康注意音效"
        },
        {
            "name": "健康温和音效",
            "pattern": r"Lang::Sounds::P3_HEALTH_GENTLE",
            "description": "检查是否使用健康温和音效"
        },
        {
            "name": "健康成功音效",
            "pattern": r"Lang::Sounds::P3_HEALTH_SUCCESS",
            "description": "检查是否使用健康成功音效"
        },
        {
            "name": "状态判断逻辑",
            "pattern": r'state == "Sad"',
            "description": "检查是否正确判断情绪状态"
        },
        {
            "name": "类型判断逻辑",
            "pattern": r'type == "posture"',
            "description": "检查是否正确判断警报类型"
        }
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if re.search(check["pattern"], content):
            print(f"✅ {check['name']}: {check['description']}")
            passed += 1
        else:
            print(f"❌ {check['name']}: {check['description']}")
    
    print(f"\n📊 代码检查结果: {passed}/{total} 通过")
    return passed == total

def check_lambda_syntax():
    """
    检查lambda函数的语法是否正确
    """
    print("\n🔍 检查lambda函数语法...")
    
    app_file = Path("main/application.cc")
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找Schedule lambda函数
    lambda_pattern = r'Schedule\(\[([^\]]+)\]\([^)]*\)\s*\{'
    matches = re.findall(lambda_pattern, content)
    
    if not matches:
        print("❌ 未找到Schedule lambda函数")
        return False
    
    for i, capture_list in enumerate(matches):
        print(f"Lambda {i+1} 捕获列表: [{capture_list}]")
        
        # 检查是否包含必要的变量
        required_vars = ["this", "message", "emotion", "type", "state"]
        missing_vars = []
        
        for var in required_vars:
            if var not in capture_list:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ 缺少变量: {missing_vars}")
            return False
        else:
            print("✅ 捕获列表包含所有必要变量")
    
    return True

def check_sound_constants():
    """
    检查音效常量是否正确定义
    """
    print("\n🔍 检查音效常量定义...")
    
    config_file = Path("main/assets/lang_config.h")
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_constants = [
        "P3_POSTURE_REMINDER",
        "P3_EMOTION_CARE",
        "P3_HEALTH_ATTENTION", 
        "P3_HEALTH_GENTLE",
        "P3_HEALTH_SUCCESS"
    ]
    
    missing_constants = []
    for constant in required_constants:
        if f"P3_{constant.split('_', 1)[1]}" in content:
            print(f"✅ {constant}")
        else:
            print(f"❌ {constant}")
            missing_constants.append(constant)
    
    return len(missing_constants) == 0

def check_logic_flow():
    """
    检查逻辑流程是否正确
    """
    print("\n🔍 检查逻辑流程...")
    
    app_file = Path("main/application.cc")
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找关键逻辑片段
    logic_checks = [
        {
            "name": "姿势警报分支",
            "pattern": r'if \(is_posture_alert\)',
            "description": "检查姿势警报判断逻辑"
        },
        {
            "name": "情绪警报分支", 
            "pattern": r'else if \(is_emotion_alert\)',
            "description": "检查情绪警报判断逻辑"
        },
        {
            "name": "默认分支",
            "pattern": r'else \{[^}]*P3_HEALTH_GENTLE',
            "description": "检查默认音效分支"
        },
        {
            "name": "音效播放",
            "pattern": r'PlaySound\(alert_sound\)',
            "description": "检查音效播放调用"
        },
        {
            "name": "成功音效",
            "pattern": r'PlaySoundImmediate\(Lang::Sounds::P3_HEALTH_SUCCESS\)',
            "description": "检查成功音效播放"
        }
    ]
    
    passed = 0
    for check in logic_checks:
        if re.search(check["pattern"], content, re.DOTALL):
            print(f"✅ {check['name']}: {check['description']}")
            passed += 1
        else:
            print(f"❌ {check['name']}: {check['description']}")
    
    return passed == len(logic_checks)

def main():
    """
    主验证函数
    """
    print("🧪 健康提醒功能代码验证")
    print("=" * 50)
    
    tests = [
        ("应用代码检查", check_application_code),
        ("Lambda语法检查", check_lambda_syntax),
        ("音效常量检查", check_sound_constants),
        ("逻辑流程检查", check_logic_flow)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 验证结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 验证通过")
    
    if passed == total:
        print("🎉 所有验证通过！代码修改正确。")
        print("\n📝 下一步:")
        print("1. 代码已修复编译错误")
        print("2. 可以重新编译项目")
        print("3. 烧录到设备进行测试")
    else:
        print("⚠️  部分验证失败，请检查代码。")
    
    return passed == total

if __name__ == "__main__":
    main()
