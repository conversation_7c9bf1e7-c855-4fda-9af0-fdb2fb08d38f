#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换录制的语音文件为P3格式
"""

import os
import sys
import subprocess
from pathlib import Path

def check_recorded_files():
    """
    检查录制的文件是否存在
    """
    print("🔍 检查录制的语音文件...")
    
    temp_dir = Path("temp_voice")
    if not temp_dir.exists():
        print("❌ temp_voice目录不存在")
        print("请先创建目录: mkdir temp_voice")
        return False
    
    required_files = {
        "posture_bad.mp3": "坐姿提醒",
        "emotion_sad.mp3": "悲伤关怀",
        "emotion_angry.mp3": "愤怒安抚", 
        "emotion_happy.mp3": "开心确认"
    }
    
    missing_files = []
    for filename, description in required_files.items():
        file_path = temp_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {filename}: {description} ({size} bytes)")
        else:
            print(f"❌ {filename}: {description} (缺失)")
            missing_files.append(filename)
    
    if missing_files:
        print(f"\n⚠️ 缺少文件: {', '.join(missing_files)}")
        print("请录制并保存这些文件到 temp_voice/ 目录")
        return False
    
    print("✅ 所有录制文件检查通过")
    return True

def convert_mp3_to_p3(mp3_file, p3_file):
    """
    使用P3转换工具转换MP3为P3格式
    """
    try:
        script_path = Path("scripts/p3_tools/convert_audio_to_p3.py")
        if not script_path.exists():
            print(f"❌ P3转换脚本不存在: {script_path}")
            return False
        
        cmd = [
            sys.executable,
            str(script_path),
            str(mp3_file),
            str(p3_file)
        ]
        
        print(f"🔄 转换: {mp3_file.name} -> {p3_file.name}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ 转换失败: {result.stderr}")
            return False
        
        if p3_file.exists():
            size = p3_file.stat().st_size
            print(f"✅ 转换成功: {p3_file.name} ({size} bytes)")
            return True
        else:
            print(f"❌ 转换后文件不存在: {p3_file}")
            return False
            
    except Exception as e:
        print(f"❌ 转换错误: {e}")
        return False

def update_health_voice_files():
    """
    更新健康语音文件
    """
    print("\n🔄 更新健康语音文件...")
    
    temp_dir = Path("temp_voice")
    target_dir = Path("main/assets/zh-CN")
    
    # 文件映射
    file_mapping = {
        "posture_bad.mp3": "health_posture_bad.p3",
        "emotion_sad.mp3": "health_emotion_sad.p3",
        "emotion_angry.mp3": "health_emotion_angry.p3",
        "emotion_happy.mp3": "health_emotion_happy.p3"
    }
    
    success_count = 0
    total_count = len(file_mapping)
    
    for mp3_name, p3_name in file_mapping.items():
        mp3_file = temp_dir / mp3_name
        p3_file = target_dir / p3_name
        
        if convert_mp3_to_p3(mp3_file, p3_file):
            success_count += 1
        else:
            print(f"❌ 转换失败: {mp3_name}")
    
    print(f"\n📊 转换结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def verify_p3_files():
    """
    验证生成的P3文件
    """
    print("\n🔍 验证生成的P3文件...")
    
    target_dir = Path("main/assets/zh-CN")
    
    p3_files = {
        "health_posture_bad.p3": "坐姿提醒",
        "health_emotion_sad.p3": "悲伤关怀",
        "health_emotion_angry.p3": "愤怒安抚",
        "health_emotion_happy.p3": "开心确认"
    }
    
    all_good = True
    for filename, description in p3_files.items():
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            if size > 100:  # 至少100字节
                print(f"✅ {filename}: {description} ({size} bytes)")
            else:
                print(f"⚠️ {filename}: {description} (文件太小: {size} bytes)")
                all_good = False
        else:
            print(f"❌ {filename}: {description} (不存在)")
            all_good = False
    
    return all_good

def main():
    """
    主函数
    """
    print("🎤 转换录制的语音文件为P3格式")
    print("=" * 50)
    
    # 检查录制文件
    if not check_recorded_files():
        print("\n❌ 请先录制语音文件")
        print("\n📝 录制指导:")
        print("1. 录制以下内容:")
        print("   - posture_bad.mp3: '我发现你的坐姿不太好，记得挺直腰背哦！'")
        print("   - emotion_sad.mp3: '我注意到你最近看起来有些难过，要不要聊聊天？'")
        print("   - emotion_angry.mp3: '你看起来有点生气，深呼吸一下，放松心情吧。'")
        print("   - emotion_happy.mp3: '你看起来心情很好呢，真棒！'")
        print("2. 保存为MP3格式")
        print("3. 放在 temp_voice/ 目录下")
        print("4. 重新运行此脚本")
        return
    
    # 转换文件
    if update_health_voice_files():
        print("\n✅ 语音文件转换成功！")
        
        # 验证文件
        if verify_p3_files():
            print("\n🎉 所有文件验证通过！")
            print("\n🚀 下一步:")
            print("1. 编译项目: idf.py build")
            print("2. 烧录设备: idf.py flash")
            print("3. 测试健康警报，应该能听到您录制的清晰语音")
            
            print("\n💡 测试方法:")
            print("- 触发坐姿检测 -> 听到您录制的坐姿提醒")
            print("- 触发情绪检测 -> 听到您录制的情绪关怀")
            
        else:
            print("\n⚠️ 部分文件验证失败，请检查转换过程")
    else:
        print("\n❌ 语音文件转换失败")
        print("请检查:")
        print("1. MP3文件是否正确")
        print("2. P3转换工具是否可用")
        print("3. 文件权限是否正确")

if __name__ == "__main__":
    main()
