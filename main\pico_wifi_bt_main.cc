#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "pico_ble_client.h"
#include "esp_netif.h"
#include "cJSON.h"
#include "wifi_data_receiver.h"
#include "ble_bridge_config.h"
#include <string>

static const char* TAG = "PICO_BLE_CLIENT";

// WiFi事件组
static EventGroupHandle_t s_wifi_event_group;
#define WIFI_CONNECTED_BIT BIT0
#define WIFI_FAIL_BIT      BIT1

// 使用配置文件中的定义
#define WIFI_SSID           WIFI_SSID
#define WIFI_PASS           WIFI_PASSWORD
#define WIFI_MAXIMUM_RETRY  WIFI_MAXIMUM_RETRY
#define TARGET_DEVICE_NAME  BLE_DEVICE_NAME_DEVKITC

// 全局变量
static PicoBleClient* g_ble_client = nullptr;
static QueueHandle_t g_wifi_data_queue = nullptr;
static int s_retry_num = 0;

// WiFi数据包结构
struct WiFiDataPacket {
    char data[512];
    size_t length;
    char source_ip[16];
    uint16_t source_port;
    uint32_t timestamp;
};

/**
 * @brief WiFi事件处理函数
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data) {
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (s_retry_num < WIFI_MAXIMUM_RETRY) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "retry to connect to the AP");
        } else {
            xEventGroupSetBits(s_wifi_event_group, WIFI_FAIL_BIT);
        }
        ESP_LOGI(TAG, "connect to the AP fail");
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "got ip:" IPSTR, IP2STR(&event->ip_info.ip));
        s_retry_num = 0;
        xEventGroupSetBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

/**
 * @brief 初始化WiFi
 */
static bool wifi_init_sta(void) {
    s_wifi_event_group = xEventGroupCreate();

    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_any_id));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_got_ip));

    wifi_config_t wifi_config = {};
    strcpy((char*)wifi_config.sta.ssid, WIFI_SSID);
    strcpy((char*)wifi_config.sta.password, WIFI_PASS);
    wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;

    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "wifi_init_sta finished.");

    /* Waiting until either the connection is established (WIFI_CONNECTED_BIT) or connection failed for the maximum
     * number of re-tries (WIFI_FAIL_BIT). The bits are set by event_handler() (see above) */
    EventBits_t bits = xEventGroupWaitBits(s_wifi_event_group,
            WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
            pdFALSE,
            pdFALSE,
            portMAX_DELAY);

    /* xEventGroupWaitBits() returns the bits before the call returned, hence we can test which event actually
     * happened. */
    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "connected to ap SSID:%s password:%s", WIFI_SSID, WIFI_PASS);
        return true;
    } else if (bits & WIFI_FAIL_BIT) {
        ESP_LOGI(TAG, "Failed to connect to SSID:%s, password:%s", WIFI_SSID, WIFI_PASS);
        return false;
    } else {
        ESP_LOGE(TAG, "UNEXPECTED EVENT");
        return false;
    }
}

/**
 * @brief WiFi数据接收回调函数
 */
static void wifi_data_received_callback(const wifi_data_packet_t* packet, void* user_data) {
    if (!packet || !packet->data) {
        ESP_LOGW(TAG, "Invalid WiFi data packet");
        return;
    }

    ESP_LOGI(TAG, "WiFi data received from %s:%d, length: %d",
             packet->client_ip, packet->client_port, packet->length);

    // 创建数据包并放入队列
    WiFiDataPacket wifi_packet;
    memset(&wifi_packet, 0, sizeof(wifi_packet));

    // 复制数据
    size_t copy_len = (packet->length < sizeof(wifi_packet.data) - 1) ?
                      packet->length : sizeof(wifi_packet.data) - 1;
    memcpy(wifi_packet.data, packet->data, copy_len);
    wifi_packet.data[copy_len] = '\0';
    wifi_packet.length = copy_len;

    // 复制源信息
    strncpy(wifi_packet.source_ip, packet->client_ip, sizeof(wifi_packet.source_ip) - 1);
    wifi_packet.source_port = packet->client_port;
    wifi_packet.timestamp = esp_timer_get_time() / 1000; // 毫秒时间戳

    // 发送到队列
    if (g_wifi_data_queue) {
        if (xQueueSend(g_wifi_data_queue, &wifi_packet, pdMS_TO_TICKS(100)) != pdTRUE) {
            ESP_LOGW(TAG, "Failed to queue WiFi data packet");
        } else {
            ESP_LOGD(TAG, "WiFi data queued successfully");
        }
    }
}

/**
 * @brief BLE连接状态回调函数
 */
static void ble_connection_callback(PicoBleClient::ConnectionState state, const esp_bd_addr_t& address) {
    if (state == PicoBleClient::CONNECTED) {
        ESP_LOGI(TAG, "BLE connected to DevKitC: %02x:%02x:%02x:%02x:%02x:%02x",
                 address[0], address[1], address[2], address[3], address[4], address[5]);
    } else if (state == PicoBleClient::DISCONNECTED) {
        ESP_LOGI(TAG, "BLE disconnected from DevKitC");
        // 可以在这里添加自动重连逻辑
    }
}

/**
 * @brief BLE数据接收回调函数
 */
static void ble_data_received_callback(const uint8_t* data, size_t length) {
    ESP_LOGI(TAG, "BLE data received: %d bytes", length);
    // 这里可以处理从DevKitC接收到的数据
    ESP_LOG_BUFFER_HEX(TAG, data, length);
}

/**
 * @brief WiFi数据转发任务
 */
static void wifi_data_forward_task(void* pvParameters) {
    WiFiDataPacket packet;

    ESP_LOGI(TAG, "WiFi data forward task started");

    while (true) {
        if (xQueueReceive(g_wifi_data_queue, &packet, portMAX_DELAY) == pdTRUE) {
            ESP_LOGI(TAG, "Processing WiFi data packet:");
            ESP_LOGI(TAG, "  Source: %s:%d", packet.source_ip, packet.source_port);
            ESP_LOGI(TAG, "  Length: %d bytes", packet.length);
            ESP_LOGI(TAG, "  Timestamp: %lu ms", packet.timestamp);

            // 检查BLE连接状态
            if (g_ble_client && g_ble_client->GetConnectionState() == PicoBleClient::CONNECTED) {
                // 创建JSON格式的数据包
                cJSON* json = cJSON_CreateObject();
                cJSON* data_item = cJSON_CreateString(packet.data);
                cJSON* source_ip = cJSON_CreateString(packet.source_ip);
                cJSON* source_port = cJSON_CreateNumber(packet.source_port);
                cJSON* timestamp = cJSON_CreateNumber(packet.timestamp);
                cJSON* length = cJSON_CreateNumber(packet.length);

                cJSON_AddItemToObject(json, "data", data_item);
                cJSON_AddItemToObject(json, "source_ip", source_ip);
                cJSON_AddItemToObject(json, "source_port", source_port);
                cJSON_AddItemToObject(json, "timestamp", timestamp);
                cJSON_AddItemToObject(json, "length", length);

                char* json_string = cJSON_Print(json);
                if (json_string) {
                    // 通过BLE发送数据到DevKitC
                    bool sent = g_ble_client->SendData((const uint8_t*)json_string, strlen(json_string));
                    if (sent) {
                        ESP_LOGI(TAG, "✅ Data forwarded via BLE successfully");
                    } else {
                        ESP_LOGE(TAG, "❌ Failed to forward data via BLE");
                    }

                    free(json_string);
                }

                cJSON_Delete(json);
            } else {
                ESP_LOGW(TAG, "⚠️ BLE not connected, cannot forward data");
            }
        }
    }
}

/**
 * @brief 状态监控任务
 */
static void status_monitor_task(void* pvParameters) {
    uint32_t last_check_time = 0;

    while (true) {
        uint32_t current_time = esp_timer_get_time() / 1000000; // 秒

        // 每30秒显示一次状态
        if (current_time - last_check_time >= 30) {
            ESP_LOGI(TAG, "=== Status Report ===");

            // WiFi状态
            esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
            esp_netif_ip_info_t ip_info;
            if (esp_netif_get_ip_info(netif, &ip_info) == ESP_OK) {
                ESP_LOGI(TAG, "WiFi: Connected, IP: " IPSTR, IP2STR(&ip_info.ip));
            } else {
                ESP_LOGI(TAG, "WiFi: Disconnected");
            }

            // BLE状态
            if (g_ble_client) {
                PicoBleClient::ConnectionState state = g_ble_client->GetConnectionState();
                const char* state_str = "Unknown";
                switch (state) {
                    case PicoBleClient::DISCONNECTED: state_str = "Disconnected"; break;
                    case PicoBleClient::SCANNING: state_str = "Scanning"; break;
                    case PicoBleClient::CONNECTING: state_str = "Connecting"; break;
                    case PicoBleClient::CONNECTED: state_str = "Connected"; break;
                }
                ESP_LOGI(TAG, "BLE: %s", state_str);

                // 显示发现的设备数量
                auto devices = g_ble_client->GetDiscoveredDevices();
                ESP_LOGI(TAG, "Discovered devices: %d", devices.size());
            }

            // 队列状态
            if (g_wifi_data_queue) {
                UBaseType_t queue_items = uxQueueMessagesWaiting(g_wifi_data_queue);
                ESP_LOGI(TAG, "WiFi data queue: %d items pending", queue_items);
            }

            ESP_LOGI(TAG, "==================");
            last_check_time = current_time;
        }

        vTaskDelay(pdMS_TO_TICKS(5000)); // 每5秒检查一次
    }
}

/**
 * @brief 初始化WiFi数据接收器
 */
static bool init_wifi_data_receiver() {
    ESP_LOGI(TAG, "Initializing WiFi data receiver...");

    wifi_data_receiver_config_t config = wifi_data_receiver_get_default_config();
    config.port = 8080;
    config.max_clients = 3;
    config.recv_timeout_ms = 10000;
    config.auto_start = true;

    esp_err_t ret = wifi_data_receiver_init(&config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi data receiver: %s", esp_err_to_name(ret));
        return false;
    }

    // 设置回调函数
    wifi_data_receiver_set_data_callback(wifi_data_received_callback, nullptr);

    // 获取并显示服务器信息
    char ip_str[16];
    if (wifi_data_receiver_get_server_ip(ip_str, sizeof(ip_str)) == ESP_OK) {
        ESP_LOGI(TAG, "📡 WiFi Data Receiver started successfully!");
        ESP_LOGI(TAG, "🌐 Server IP: %s", ip_str);
        ESP_LOGI(TAG, "🔌 Server Port: %d", config.port);
        ESP_LOGI(TAG, "✅ Ready to receive WiFi data and forward via BLE!");
    }

    return true;
}

/**
 * @brief 初始化BLE客户端
 */
static bool init_ble_client() {
    ESP_LOGI(TAG, "Initializing BLE client...");

    // 获取BLE客户端实例
    g_ble_client = PicoBleClient::GetInstance();
    if (!g_ble_client) {
        ESP_LOGE(TAG, "Failed to get BLE client instance");
        return false;
    }

    // 设置设备名称
    g_ble_client->SetDeviceName("ESP32S3-PICO-Client");
    g_ble_client->SetTargetDeviceName(TARGET_DEVICE_NAME);

    // 设置回调函数
    g_ble_client->SetConnectionCallback(ble_connection_callback);
    g_ble_client->SetDataCallback(ble_data_received_callback);

    // 初始化BLE客户端
    if (!g_ble_client->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize BLE client");
        PicoBleClient::DestroyInstance();
        g_ble_client = nullptr;
        return false;
    }

    ESP_LOGI(TAG, "BLE client initialized successfully");
    return true;
}

/**
 * @brief BLE设备扫描和连接任务
 */
static void ble_scan_connect_task(void* pvParameters) {
    ESP_LOGI(TAG, "BLE scan and connect task started");

    while (true) {
        if (g_ble_client && g_ble_client->GetConnectionState() == PicoBleClient::DISCONNECTED) {
            ESP_LOGI(TAG, "Starting BLE scan for target device: %s", TARGET_DEVICE_NAME);

            // 开始扫描
            if (g_ble_client->StartScan(30)) {
                // 等待扫描完成
                while (g_ble_client->IsScanning()) {
                    vTaskDelay(pdMS_TO_TICKS(1000));
                }

                // 查找目标设备
                auto devices = g_ble_client->GetDiscoveredDevices();
                ESP_LOGI(TAG, "Found %d BLE devices", devices.size());

                bool target_found = false;
                for (const auto& device : devices) {
                    ESP_LOGI(TAG, "Device: %s, RSSI: %d",
                             device.name.c_str(), device.rssi);

                    if (device.name == TARGET_DEVICE_NAME) {
                        ESP_LOGI(TAG, "Target device found! Attempting to connect...");

                        if (g_ble_client->Connect(device.address)) {
                            target_found = true;
                            ESP_LOGI(TAG, "Connection initiated to target device");
                            break;
                        } else {
                            ESP_LOGE(TAG, "Failed to initiate connection");
                        }
                    }
                }

                if (!target_found) {
                    ESP_LOGW(TAG, "Target device not found, will retry in 10 seconds");
                }
            } else {
                ESP_LOGE(TAG, "Failed to start BLE scan");
            }
        }

        vTaskDelay(pdMS_TO_TICKS(10000)); // 每10秒检查一次连接状态
    }
}

/**
 * @brief 主函数
 */
extern "C" void app_main(void) {
    ESP_LOGI(TAG, "ESP32-S3 PICO-1 WiFi-BLE Client Starting...");

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 创建WiFi数据队列
    g_wifi_data_queue = xQueueCreate(20, sizeof(WiFiDataPacket));
    if (!g_wifi_data_queue) {
        ESP_LOGE(TAG, "Failed to create WiFi data queue");
        return;
    }

    // 初始化WiFi
    ESP_LOGI(TAG, "Initializing WiFi...");
    if (!wifi_init_sta()) {
        ESP_LOGE(TAG, "Failed to connect to WiFi");
        return;
    }

    // 获取并显示IP地址
    esp_netif_t* netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
    esp_netif_ip_info_t ip_info;
    esp_netif_get_ip_info(netif, &ip_info);
    ESP_LOGI(TAG, "WiFi connected. IP: " IPSTR, IP2STR(&ip_info.ip));

    // 初始化WiFi数据接收器
    if (!init_wifi_data_receiver()) {
        ESP_LOGE(TAG, "Failed to initialize WiFi data receiver");
        return;
    }

    // 初始化BLE客户端
    if (!init_ble_client()) {
        ESP_LOGE(TAG, "Failed to initialize BLE client");
        return;
    }

    ESP_LOGI(TAG, "=== ESP32-S3 PICO-1 WiFi-BLE Client Started ===");
    ESP_LOGI(TAG, "📡 WiFi Server: " IPSTR ":8080", IP2STR(&ip_info.ip));
    ESP_LOGI(TAG, "📱 BLE Client: Searching for %s", TARGET_DEVICE_NAME);
    ESP_LOGI(TAG, "🔄 Ready to receive WiFi data and forward via BLE");
    ESP_LOGI(TAG, "===============================================");

    // 创建任务
    xTaskCreate(wifi_data_forward_task, "wifi_forward", 6144, NULL, 6, NULL);
    xTaskCreate(ble_scan_connect_task, "ble_scan", 4096, NULL, 5, NULL);
    xTaskCreate(status_monitor_task, "status_monitor", 3072, NULL, 3, NULL);

    ESP_LOGI(TAG, "All tasks created successfully");

    // 主循环
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(1000));

        // 这里可以添加其他周期性任务
        // 例如：LED指示、按键检测、看门狗喂狗等
    }

    // 清理资源（通常不会执行到这里）
    if (g_ble_client) {
        g_ble_client->Disconnect();
        PicoBleClient::DestroyInstance();
        g_ble_client = nullptr;
    }

    if (g_wifi_data_queue) {
        vQueueDelete(g_wifi_data_queue);
        g_wifi_data_queue = nullptr;
    }
}
