#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示语音文件
使用文本转语音创建临时语音文件用于测试
"""

import os
import json
import shutil
from pathlib import Path

def create_demo_voice_files():
    """
    创建演示语音文件（使用现有音效作为占位符）
    """
    print("🎤 创建演示语音文件...")
    
    # 创建目标目录
    target_dir = Path("main/assets/zh-CN")
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 使用现有的音效文件作为模板
    source_files = {
        "activation.p3": [
            "health_posture_bad.p3",
            "health_emotion_sad.p3", 
            "health_emotion_angry.p3"
        ],
        "welcome.p3": [
            "health_posture_good.p3",
            "health_emotion_happy.p3",
            "health_gentle_reminder.p3"
        ],
        "upgrade.p3": [
            "health_break_reminder.p3",
            "health_eye_care.p3",
            "health_success_confirm.p3"
        ]
    }
    
    created_files = []
    
    for source, targets in source_files.items():
        source_path = target_dir / source
        if source_path.exists():
            for target in targets:
                target_path = target_dir / target
                try:
                    shutil.copy2(source_path, target_path)
                    size = target_path.stat().st_size
                    print(f"✅ 创建演示文件: {target} ({size} bytes)")
                    created_files.append(target)
                except Exception as e:
                    print(f"❌ 创建失败 {target}: {e}")
        else:
            print(f"⚠️ 源文件不存在: {source}")
    
    return created_files

def update_lang_config_for_demo():
    """
    为演示文件更新语言配置
    """
    print("🔄 更新语言配置以支持新语音文件...")
    
    # 新增的语音文件常量定义
    new_constants = """
        // 健康提醒预录制语音
        extern const char p3_health_posture_bad_start[] asm("_binary_health_posture_bad_p3_start");
        extern const char p3_health_posture_bad_end[] asm("_binary_health_posture_bad_p3_end");
        static const std::string_view P3_HEALTH_POSTURE_BAD {
        static_cast<const char*>(p3_health_posture_bad_start),
        static_cast<size_t>(p3_health_posture_bad_end - p3_health_posture_bad_start)
        };

        extern const char p3_health_posture_good_start[] asm("_binary_health_posture_good_p3_start");
        extern const char p3_health_posture_good_end[] asm("_binary_health_posture_good_p3_end");
        static const std::string_view P3_HEALTH_POSTURE_GOOD {
        static_cast<const char*>(p3_health_posture_good_start),
        static_cast<size_t>(p3_health_posture_good_end - p3_health_posture_good_start)
        };

        extern const char p3_health_emotion_sad_start[] asm("_binary_health_emotion_sad_p3_start");
        extern const char p3_health_emotion_sad_end[] asm("_binary_health_emotion_sad_p3_end");
        static const std::string_view P3_HEALTH_EMOTION_SAD {
        static_cast<const char*>(p3_health_emotion_sad_start),
        static_cast<size_t>(p3_health_emotion_sad_end - p3_health_emotion_sad_start)
        };

        extern const char p3_health_emotion_angry_start[] asm("_binary_health_emotion_angry_p3_start");
        extern const char p3_health_emotion_angry_end[] asm("_binary_health_emotion_angry_p3_end");
        static const std::string_view P3_HEALTH_EMOTION_ANGRY {
        static_cast<const char*>(p3_health_emotion_angry_start),
        static_cast<size_t>(p3_health_emotion_angry_end - p3_health_emotion_angry_start)
        };

        extern const char p3_health_emotion_happy_start[] asm("_binary_health_emotion_happy_p3_start");
        extern const char p3_health_emotion_happy_end[] asm("_binary_health_emotion_happy_p3_end");
        static const std::string_view P3_HEALTH_EMOTION_HAPPY {
        static_cast<const char*>(p3_health_emotion_happy_start),
        static_cast<size_t>(p3_health_emotion_happy_end - p3_health_emotion_happy_start)
        };

        extern const char p3_health_gentle_reminder_start[] asm("_binary_health_gentle_reminder_p3_start");
        extern const char p3_health_gentle_reminder_end[] asm("_binary_health_gentle_reminder_p3_end");
        static const std::string_view P3_HEALTH_GENTLE_REMINDER {
        static_cast<const char*>(p3_health_gentle_reminder_start),
        static_cast<size_t>(p3_health_gentle_reminder_end - p3_health_gentle_reminder_start)
        };

        extern const char p3_health_success_confirm_start[] asm("_binary_health_success_confirm_p3_start");
        extern const char p3_health_success_confirm_end[] asm("_binary_health_success_confirm_p3_end");
        static const std::string_view P3_HEALTH_SUCCESS_CONFIRM {
        static_cast<const char*>(p3_health_success_confirm_start),
        static_cast<size_t>(p3_health_success_confirm_end - p3_health_success_confirm_start)
        };
"""
    
    # 将常量定义写入临时文件
    with open("temp_health_voice_constants.h", "w", encoding="utf-8") as f:
        f.write(new_constants)
    
    print("✅ 创建临时常量定义文件: temp_health_voice_constants.h")
    print("📝 请将此文件内容添加到 main/assets/lang_config.h 的 Sounds 命名空间中")

def create_quick_test_script():
    """
    创建快速测试脚本
    """
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
验证语音文件是否正确创建
"""

from pathlib import Path

def test_voice_files():
    """
    测试语音文件
    """
    print("🧪 测试语音文件...")
    
    target_dir = Path("main/assets/zh-CN")
    
    expected_files = [
        "health_posture_bad.p3",
        "health_posture_good.p3", 
        "health_emotion_sad.p3",
        "health_emotion_angry.p3",
        "health_emotion_happy.p3",
        "health_gentle_reminder.p3",
        "health_success_confirm.p3"
    ]
    
    success_count = 0
    for filename in expected_files:
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {filename} ({size} bytes)")
            success_count += 1
        else:
            print(f"❌ {filename} (不存在)")
    
    print(f"\\n📊 测试结果: {success_count}/{len(expected_files)} 文件存在")
    
    if success_count == len(expected_files):
        print("🎉 所有语音文件都已创建！")
        print("\\n下一步:")
        print("1. 将 temp_health_voice_constants.h 的内容添加到 lang_config.h")
        print("2. 编译项目: idf.py build")
        print("3. 烧录测试: idf.py flash")
    else:
        print("⚠️ 部分文件缺失，请检查创建过程")

if __name__ == "__main__":
    test_voice_files()
'''
    
    with open("scripts/test_voice_files.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✅ 创建测试脚本: scripts/test_voice_files.py")

def main():
    """
    主函数
    """
    print("🎵 创建演示语音文件工具")
    print("=" * 40)
    
    # 创建演示语音文件
    created_files = create_demo_voice_files()
    
    if created_files:
        print(f"\\n✅ 成功创建 {len(created_files)} 个演示语音文件")
        
        # 更新配置
        update_lang_config_for_demo()
        
        # 创建测试脚本
        create_quick_test_script()
        
        print("\\n🎯 下一步操作:")
        print("1. 查看并复制 temp_health_voice_constants.h 的内容")
        print("2. 将内容添加到 main/assets/lang_config.h 的 Sounds 命名空间")
        print("3. 运行测试: python scripts/test_voice_files.py")
        print("4. 编译项目: idf.py build")
        print("5. 烧录测试: idf.py flash")
        
        print("\\n💡 说明:")
        print("- 这些是演示文件，使用现有音效作为占位符")
        print("- 实际使用时，请录制真实的语音内容")
        print("- 演示文件可以验证系统是否正常工作")
        
    else:
        print("❌ 未能创建演示文件，请检查源文件是否存在")

if __name__ == "__main__":
    main()
