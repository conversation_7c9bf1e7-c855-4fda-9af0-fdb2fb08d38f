# ESP32-S3 PICO-1 BLE客户端构建脚本
# 用于编译PICO作为BLE客户端的固件

param(
    [string]$Action = "build",  # build, flash, monitor, clean
    [string]$Port = "COM3"      # 串口端口
)

$ErrorActionPreference = "Stop"

Write-Host "ESP32-S3 PICO-1 BLE Client Build Script" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# 检查ESP-IDF环境
if (-not $env:IDF_PATH) {
    Write-Host "Error: ESP-IDF environment not found. Please run setup_esp_idf_5.4.2.ps1 first." -ForegroundColor Red
    exit 1
}

# 设置项目配置
$PROJECT_NAME = "esp32_pico_ble_client"
$CMAKE_FILE = "CMakeLists_PICO.txt"
$MAIN_FILE = "main/pico_wifi_bt_main.cc"

Write-Host "Project: $PROJECT_NAME" -ForegroundColor Cyan
Write-Host "Target: ESP32-S3 PICO-1" -ForegroundColor Cyan
Write-Host "Function: WiFi Data Receiver + BLE Client" -ForegroundColor Cyan
Write-Host ""

# 检查必要文件
$required_files = @(
    $CMAKE_FILE,
    $MAIN_FILE,
    "main/pico_ble_client.h",
    "components/wifi_data_receiver"
)

foreach ($file in $required_files) {
    if (-not (Test-Path $file)) {
        Write-Host "Error: Required file not found: $file" -ForegroundColor Red
        exit 1
    }
}

# 备份原始CMakeLists.txt
if (Test-Path "CMakeLists.txt") {
    Copy-Item "CMakeLists.txt" "CMakeLists.txt.backup" -Force
    Write-Host "Backed up original CMakeLists.txt" -ForegroundColor Yellow
}

# 使用PICO专用的CMakeLists.txt
Copy-Item $CMAKE_FILE "CMakeLists.txt" -Force
Write-Host "Using PICO-specific CMakeLists.txt" -ForegroundColor Green

try {
    switch ($Action.ToLower()) {
        "build" {
            Write-Host "Building PICO BLE Client firmware..." -ForegroundColor Yellow
            idf.py build
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Build completed successfully!" -ForegroundColor Green
                Write-Host ""
                Write-Host "Firmware ready for ESP32-S3 PICO-1" -ForegroundColor Cyan
                Write-Host "Features:" -ForegroundColor Cyan
                Write-Host "  - WiFi Data Receiver (Port 8080)" -ForegroundColor White
                Write-Host "  - BLE Client (connects to DevKitC)" -ForegroundColor White
                Write-Host "  - Data forwarding via BLE" -ForegroundColor White
            } else {
                Write-Host "Build failed!" -ForegroundColor Red
                exit 1
            }
        }
        
        "flash" {
            Write-Host "Flashing PICO BLE Client firmware to $Port..." -ForegroundColor Yellow
            idf.py -p $Port flash
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Flash completed successfully!" -ForegroundColor Green
            } else {
                Write-Host "Flash failed!" -ForegroundColor Red
                exit 1
            }
        }
        
        "monitor" {
            Write-Host "Starting serial monitor on $Port..." -ForegroundColor Yellow
            Write-Host "Press Ctrl+] to exit monitor" -ForegroundColor Cyan
            idf.py -p $Port monitor
        }
        
        "flash-monitor" {
            Write-Host "Flashing and monitoring PICO BLE Client..." -ForegroundColor Yellow
            idf.py -p $Port flash monitor
        }
        
        "clean" {
            Write-Host "Cleaning build files..." -ForegroundColor Yellow
            idf.py clean
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Clean completed!" -ForegroundColor Green
            }
        }
        
        "menuconfig" {
            Write-Host "Opening configuration menu..." -ForegroundColor Yellow
            idf.py menuconfig
        }
        
        default {
            Write-Host "Usage: .\build_pico_ble_client.ps1 [-Action <action>] [-Port <port>]" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "Actions:" -ForegroundColor Cyan
            Write-Host "  build         - Build the firmware" -ForegroundColor White
            Write-Host "  flash         - Flash to device" -ForegroundColor White
            Write-Host "  monitor       - Start serial monitor" -ForegroundColor White
            Write-Host "  flash-monitor - Flash and monitor" -ForegroundColor White
            Write-Host "  clean         - Clean build files" -ForegroundColor White
            Write-Host "  menuconfig    - Open configuration menu" -ForegroundColor White
            Write-Host ""
            Write-Host "Examples:" -ForegroundColor Cyan
            Write-Host "  .\build_pico_ble_client.ps1 -Action build" -ForegroundColor White
            Write-Host "  .\build_pico_ble_client.ps1 -Action flash -Port COM5" -ForegroundColor White
            Write-Host "  .\build_pico_ble_client.ps1 -Action flash-monitor" -ForegroundColor White
        }
    }
} finally {
    # 恢复原始CMakeLists.txt
    if (Test-Path "CMakeLists.txt.backup") {
        Move-Item "CMakeLists.txt.backup" "CMakeLists.txt" -Force
        Write-Host "Restored original CMakeLists.txt" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "PICO BLE Client build script completed." -ForegroundColor Green
