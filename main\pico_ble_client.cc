#include "pico_ble_client.h"
#include "esp_log.h"
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_gap_ble_api.h"
#include "esp_gattc_api.h"
#include "esp_gatt_defs.h"
#include "esp_bt_device.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <cstring>

static const char* TAG = "PicoBleClient";

// 单例实例
PicoBleClient* PicoBleClient::instance_ = nullptr;

// GATT服务和特征UUID
#define REMOTE_SERVICE_UUID        0x00FF
#define REMOTE_NOTIFY_CHAR_UUID    0xFF01

// 应用ID
#define GATTC_APP_ID               0x56

PicoBleClient* PicoBleClient::GetInstance() {
    if (instance_ == nullptr) {
        instance_ = new PicoBleClient();
    }
    return instance_;
}

void PicoBleClient::DestroyInstance() {
    if (instance_ != nullptr) {
        delete instance_;
        instance_ = nullptr;
    }
}

PicoBleClient::PicoBleClient() 
    : connection_state_(DISCONNECTED)
    , initialized_(false)
    , scanning_(false)
    , gattc_if_(ESP_GATT_IF_NONE)
    , conn_id_(0)
    , service_handle_(0)
    , char_handle_(0)
    , target_found_(false)
    , scan_duration_(30)
    , packets_sent_(0)
    , bytes_sent_(0)
    , connection_attempts_(0) {
    
    memset(target_address_, 0, sizeof(target_address_));
    discovered_devices_.clear();
}

PicoBleClient::~PicoBleClient() {
    Deinitialize();
}

bool PicoBleClient::Initialize() {
    if (initialized_) {
        ESP_LOGW(TAG, "BLE client already initialized");
        return true;
    }
    
    ESP_LOGI(TAG, "Initializing BLE client");
    
    // 初始化蓝牙控制器
    if (!InitializeBluetooth()) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth");
        return false;
    }
    
    // 初始化BLE
    if (!InitializeBle()) {
        ESP_LOGE(TAG, "Failed to initialize BLE");
        return false;
    }
    
    // 设置扫描参数
    if (!SetupScanParams()) {
        ESP_LOGE(TAG, "Failed to setup scan parameters");
        return false;
    }
    
    // 注册GATT客户端应用
    esp_err_t ret = esp_ble_gattc_app_register(GATTC_APP_ID);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATT client app: %s", esp_err_to_name(ret));
        return false;
    }
    
    initialized_ = true;
    ESP_LOGI(TAG, "BLE client initialized successfully");
    return true;
}

void PicoBleClient::Deinitialize() {
    if (!initialized_) {
        return;
    }
    
    ESP_LOGI(TAG, "Deinitializing BLE client");
    
    if (connection_state_ == CONNECTED) {
        Disconnect();
    }
    
    if (scanning_) {
        StopScan();
    }
    
    esp_bluedroid_disable();
    esp_bluedroid_deinit();
    esp_bt_controller_disable();
    esp_bt_controller_deinit();
    
    initialized_ = false;
    ESP_LOGI(TAG, "BLE client deinitialized");
}

bool PicoBleClient::InitializeBluetooth() {
    ESP_LOGI(TAG, "Initializing Bluetooth controller");
    
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    esp_err_t ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable BT controller: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable bluedroid: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGI(TAG, "Bluetooth controller initialized successfully");
    return true;
}

bool PicoBleClient::InitializeBle() {
    ESP_LOGI(TAG, "Initializing BLE");
    
    // 注册GAP回调
    esp_err_t ret = esp_ble_gap_register_callback(StaticGapCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GAP callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    // 注册GATTC回调
    ret = esp_ble_gattc_register_callback(StaticGattcCallback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GATTC callback: %s", esp_err_to_name(ret));
        return false;
    }
    
    ESP_LOGI(TAG, "BLE initialized successfully");
    return true;
}

bool PicoBleClient::SetupScanParams() {
    ESP_LOGI(TAG, "Setting up scan parameters");
    
    static esp_ble_scan_params_t ble_scan_params = {
        .scan_type              = BLE_SCAN_TYPE_ACTIVE,
        .own_addr_type          = BLE_ADDR_TYPE_PUBLIC,
        .scan_filter_policy     = BLE_SCAN_FILTER_ALLOW_ALL,
        .scan_interval          = 0x50,
        .scan_window            = 0x30,
        .scan_duplicate         = BLE_SCAN_DUPLICATE_DISABLE
    };
    
    esp_err_t ret = esp_ble_gap_set_scan_params(&ble_scan_params);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set scan params: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool PicoBleClient::StartScan(uint32_t duration) {
    if (!initialized_) {
        ESP_LOGE(TAG, "BLE client not initialized");
        return false;
    }
    
    if (scanning_) {
        ESP_LOGW(TAG, "Already scanning");
        return true;
    }
    
    ESP_LOGI(TAG, "Starting BLE scan for %lu seconds", duration);
    
    // 清除之前发现的设备
    discovered_devices_.clear();
    scan_duration_ = duration;
    
    esp_err_t ret = esp_ble_gap_start_scanning(duration);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start scanning: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool PicoBleClient::StopScan() {
    if (!scanning_) {
        return true;
    }
    
    ESP_LOGI(TAG, "Stopping BLE scan");
    
    esp_err_t ret = esp_ble_gap_stop_scanning();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop scanning: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool PicoBleClient::Connect(const esp_bd_addr_t& address) {
    if (!initialized_) {
        ESP_LOGE(TAG, "BLE client not initialized");
        return false;
    }
    
    if (connection_state_ == CONNECTED || connection_state_ == CONNECTING) {
        ESP_LOGW(TAG, "Already connected or connecting");
        return false;
    }
    
    ESP_LOGI(TAG, "Connecting to device: %02x:%02x:%02x:%02x:%02x:%02x",
             address[0], address[1], address[2], address[3], address[4], address[5]);
    
    connection_state_ = CONNECTING;
    memcpy(target_address_, address, sizeof(esp_bd_addr_t));
    connection_attempts_++;
    
    esp_err_t ret = esp_ble_gattc_open(gattc_if_, const_cast<esp_bd_addr_t&>(address), BLE_ADDR_TYPE_PUBLIC, true);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to connect: %s", esp_err_to_name(ret));
        connection_state_ = DISCONNECTED;
        return false;
    }
    
    return true;
}

bool PicoBleClient::Disconnect() {
    if (connection_state_ != CONNECTED) {
        ESP_LOGW(TAG, "Not connected");
        return true;
    }
    
    ESP_LOGI(TAG, "Disconnecting from device");
    
    esp_err_t ret = esp_ble_gattc_close(gattc_if_, conn_id_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to disconnect: %s", esp_err_to_name(ret));
        return false;
    }
    
    return true;
}

bool PicoBleClient::SendData(const uint8_t* data, size_t length) {
    if (connection_state_ != CONNECTED) {
        ESP_LOGE(TAG, "Not connected to any device");
        return false;
    }
    
    if (char_handle_ == 0) {
        ESP_LOGE(TAG, "Characteristic handle not found");
        return false;
    }
    
    ESP_LOGI(TAG, "Sending data: %zu bytes", length);
    
    esp_err_t ret = esp_ble_gattc_write_char(gattc_if_, conn_id_, char_handle_, 
                                             length, const_cast<uint8_t*>(data), 
                                             ESP_GATT_WRITE_TYPE_RSP, ESP_GATT_AUTH_REQ_NONE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to write characteristic: %s", esp_err_to_name(ret));
        return false;
    }
    
    packets_sent_++;
    bytes_sent_ += length;
    return true;
}

bool PicoBleClient::SendString(const std::string& message) {
    return SendData((const uint8_t*)message.c_str(), message.length());
}

void PicoBleClient::SetConnectionCallback(connection_callback_t callback) {
    connection_callback_ = callback;
}

void PicoBleClient::SetDataCallback(data_callback_t callback) {
    data_callback_ = callback;
}

void PicoBleClient::SetStatusCallback(status_callback_t callback) {
    status_callback_ = callback;
}

void PicoBleClient::SetDeviceName(const std::string& name) {
    if (initialized_) {
        esp_ble_gap_set_device_name(name.c_str());
    }
}

void PicoBleClient::SetTargetDeviceName(const std::string& name) {
    target_device_name_ = name;
}

const std::vector<PicoBleClient::DiscoveredDevice>& PicoBleClient::GetDiscoveredDevices() const {
    return discovered_devices_;
}

void PicoBleClient::GetStatistics(uint32_t& packets_sent, uint32_t& bytes_sent, uint32_t& connection_attempts) const {
    packets_sent = packets_sent_;
    bytes_sent = bytes_sent_;
    connection_attempts = connection_attempts_;
}

void PicoBleClient::ResetStatistics() {
    packets_sent_ = 0;
    bytes_sent_ = 0;
    connection_attempts_ = 0;
}

void PicoBleClient::StaticGapCallback(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGapEvent(event, param);
    }
}

void PicoBleClient::StaticGattcCallback(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) {
    if (instance_) {
        instance_->HandleGattcEvent(event, gattc_if, param);
    }
}

void PicoBleClient::HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param) {
    switch (event) {
        case ESP_GAP_BLE_SCAN_PARAM_SET_COMPLETE_EVT:
            ESP_LOGI(TAG, "Scan parameters set complete");
            break;

        case ESP_GAP_BLE_SCAN_START_COMPLETE_EVT:
            if (param->scan_start_cmpl.status == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "Scan started successfully");
                scanning_ = true;
                connection_state_ = SCANNING;
            } else {
                ESP_LOGE(TAG, "Failed to start scan");
                scanning_ = false;
            }
            break;

        case ESP_GAP_BLE_SCAN_RESULT_EVT:
            HandleDeviceDiscovered(param);
            break;

        case ESP_GAP_BLE_SCAN_STOP_COMPLETE_EVT:
            if (param->scan_stop_cmpl.status == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "Scan stopped successfully");
                scanning_ = false;
                if (connection_state_ == SCANNING) {
                    connection_state_ = DISCONNECTED;
                }
            } else {
                ESP_LOGE(TAG, "Failed to stop scan");
            }
            break;

        default:
            break;
    }
}

void PicoBleClient::HandleGattcEvent(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_ble_gattc_cb_param_t* param) {
    switch (event) {
        case ESP_GATTC_REG_EVT:
            if (param->reg.status == ESP_GATT_OK) {
                gattc_if_ = gattc_if;
                ESP_LOGI(TAG, "GATT client registered, app_id: %04x", param->reg.app_id);
            } else {
                ESP_LOGE(TAG, "GATT client register failed, app_id: %04x, status: %d",
                         param->reg.app_id, param->reg.status);
            }
            break;

        case ESP_GATTC_OPEN_EVT:
            if (param->open.status == ESP_GATT_OK) {
                ESP_LOGI(TAG, "Connection opened successfully");
                conn_id_ = param->open.conn_id;
                HandleConnectionEstablished();

                // 开始服务发现
                esp_ble_gattc_search_service(gattc_if_, conn_id_, nullptr);
            } else {
                ESP_LOGE(TAG, "Failed to open connection, status: %d", param->open.status);
                connection_state_ = DISCONNECTED;
            }
            break;

        case ESP_GATTC_CLOSE_EVT:
            ESP_LOGI(TAG, "Connection closed");
            HandleConnectionClosed();
            break;

        case ESP_GATTC_SEARCH_RES_EVT:
            if (param->search_res.srvc_id.uuid.len == ESP_UUID_LEN_16 &&
                param->search_res.srvc_id.uuid.uuid.uuid16 == REMOTE_SERVICE_UUID) {
                ESP_LOGI(TAG, "Found target service");
                service_handle_ = param->search_res.start_handle;
            }
            break;

        case ESP_GATTC_SEARCH_CMPL_EVT:
            if (param->search_cmpl.status == ESP_GATT_OK) {
                ESP_LOGI(TAG, "Service discovery completed");
                if (service_handle_ != 0) {
                    // 获取特征
                    esp_ble_gattc_get_all_char(gattc_if_, conn_id_, service_handle_,
                                               service_handle_ + 10, nullptr);
                }
            }
            break;

        case ESP_GATTC_GET_CHAR_EVT:
            if (param->get_char.status == ESP_GATT_OK &&
                param->get_char.char_id.uuid.len == ESP_UUID_LEN_16 &&
                param->get_char.char_id.uuid.uuid.uuid16 == REMOTE_NOTIFY_CHAR_UUID) {
                ESP_LOGI(TAG, "Found target characteristic");
                char_handle_ = param->get_char.char_handle;
                connection_state_ = CONNECTED;

                if (connection_callback_) {
                    connection_callback_(CONNECTED, target_address_);
                }
            }
            break;

        case ESP_GATTC_WRITE_EVT:
            if (param->write.status == ESP_GATT_OK) {
                ESP_LOGD(TAG, "Write operation completed successfully");
            } else {
                ESP_LOGE(TAG, "Write operation failed, status: %d", param->write.status);
            }
            break;

        case ESP_GATTC_NOTIFY_EVT:
            ESP_LOGI(TAG, "Received notification: %d bytes", param->notify.value_len);
            HandleDataReceived(param->notify.value, param->notify.value_len);
            break;

        default:
            break;
    }
}

void PicoBleClient::HandleDeviceDiscovered(esp_ble_gap_cb_param_t* param) {
    esp_ble_gap_cb_param_t* scan_result = param;

    if (scan_result->scan_rst.search_evt == ESP_GAP_SEARCH_INQ_RES_EVT) {
        // 获取设备名称
        uint8_t* adv_name = nullptr;
        uint8_t adv_name_len = 0;
        adv_name = esp_ble_resolve_adv_data(scan_result->scan_rst.ble_adv,
                                           ESP_BLE_AD_TYPE_NAME_CMPL, &adv_name_len);

        std::string device_name;
        if (adv_name != nullptr && adv_name_len > 0) {
            device_name = std::string((char*)adv_name, adv_name_len);
        }

        ESP_LOGI(TAG, "Found device: %s, RSSI: %d",
                 device_name.c_str(), scan_result->scan_rst.rssi);

        // 添加到发现设备列表
        DiscoveredDevice device;
        memcpy(device.address, scan_result->scan_rst.bda, sizeof(esp_bd_addr_t));
        device.name = device_name;
        device.rssi = scan_result->scan_rst.rssi;
        device.connectable = (scan_result->scan_rst.ble_evt_type == ESP_BLE_EVT_CONN_ADV);

        discovered_devices_.push_back(device);

        // 检查是否是目标设备
        if (!target_device_name_.empty() && device_name == target_device_name_) {
            ESP_LOGI(TAG, "Target device found: %s", target_device_name_.c_str());
            target_found_ = true;
            memcpy(target_address_, scan_result->scan_rst.bda, sizeof(esp_bd_addr_t));

            // 停止扫描并连接
            StopScan();
        }
    }
}

void PicoBleClient::HandleConnectionEstablished() {
    ESP_LOGI(TAG, "BLE connection established");
    SendStatusMessage("BLE connection established");
}

void PicoBleClient::HandleConnectionClosed() {
    ESP_LOGI(TAG, "BLE connection closed");
    connection_state_ = DISCONNECTED;
    conn_id_ = 0;
    service_handle_ = 0;
    char_handle_ = 0;

    if (connection_callback_) {
        connection_callback_(DISCONNECTED, target_address_);
    }

    SendStatusMessage("BLE connection closed");
}

void PicoBleClient::HandleDataReceived(const uint8_t* data, size_t length) {
    ESP_LOGI(TAG, "Data received: %zu bytes", length);

    if (data_callback_) {
        data_callback_(data, length);
    }
}

void PicoBleClient::SendStatusMessage(const std::string& message) {
    ESP_LOGI(TAG, "Status: %s", message.c_str());

    if (status_callback_) {
        status_callback_(message);
    }
}
