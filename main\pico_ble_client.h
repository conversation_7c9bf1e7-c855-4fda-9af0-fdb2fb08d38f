#ifndef PICO_BLE_CLIENT_H
#define PICO_BLE_CLIENT_H

#include "esp_gap_ble_api.h"
#include "esp_gattc_api.h"
#include "esp_bt_device.h"
#include <string>
#include <functional>
#include <vector>

/**
 * @brief ESP32-S3 PICO-1 BLE客户端类
 * 
 * 专门用于连接ESP32-S3 DevKitC-1并发送WiFi数据
 */
class PicoBleClient {
public:
    // 连接状态枚举
    enum ConnectionState {
        DISCONNECTED,
        SCANNING,
        CONNECTING,
        CONNECTED
    };
    
    // 回调函数类型定义
    typedef std::function<void(ConnectionState state, const esp_bd_addr_t& address)> connection_callback_t;
    typedef std::function<void(const uint8_t* data, size_t length)> data_callback_t;
    typedef std::function<void(const std::string& message)> status_callback_t;
    
    // 设备发现结构体
    struct DiscoveredDevice {
        esp_bd_addr_t address;
        std::string name;
        int rssi;
        bool connectable;
    };

private:
    // 单例模式
    static PicoBleClient* instance_;
    
    // 连接状态
    ConnectionState connection_state_;
    bool initialized_;
    bool scanning_;
    
    // GATT客户端接口
    esp_gatt_if_t gattc_if_;
    uint16_t conn_id_;
    uint16_t service_handle_;
    uint16_t char_handle_;
    
    // 目标设备信息
    std::string target_device_name_;
    esp_bd_addr_t target_address_;
    bool target_found_;
    
    // 发现的设备列表
    std::vector<DiscoveredDevice> discovered_devices_;
    
    // 回调函数
    connection_callback_t connection_callback_;
    data_callback_t data_callback_;
    status_callback_t status_callback_;
    
    // 扫描参数
    uint32_t scan_duration_;
    
    // 统计信息
    uint32_t packets_sent_;
    uint32_t bytes_sent_;
    uint32_t connection_attempts_;

public:
    /**
     * @brief 获取单例实例
     * 
     * @return PicoBleClient* 实例指针
     */
    static PicoBleClient* GetInstance();
    
    /**
     * @brief 销毁单例实例
     */
    static void DestroyInstance();
    
    /**
     * @brief 初始化BLE客户端
     * 
     * @return true 成功
     * @return false 失败
     */
    bool Initialize();
    
    /**
     * @brief 反初始化BLE客户端
     */
    void Deinitialize();
    
    /**
     * @brief 开始扫描设备
     * 
     * @param duration 扫描持续时间（秒）
     * @return true 成功
     * @return false 失败
     */
    bool StartScan(uint32_t duration = 30);
    
    /**
     * @brief 停止扫描设备
     * 
     * @return true 成功
     * @return false 失败
     */
    bool StopScan();
    
    /**
     * @brief 连接到目标设备
     * 
     * @param address 设备地址
     * @return true 成功
     * @return false 失败
     */
    bool Connect(const esp_bd_addr_t& address);
    
    /**
     * @brief 断开连接
     * 
     * @return true 成功
     * @return false 失败
     */
    bool Disconnect();
    
    /**
     * @brief 发送数据到服务器
     * 
     * @param data 数据
     * @param length 数据长度
     * @return true 成功
     * @return false 失败
     */
    bool SendData(const uint8_t* data, size_t length);
    
    /**
     * @brief 发送字符串到服务器
     * 
     * @param message 消息
     * @return true 成功
     * @return false 失败
     */
    bool SendString(const std::string& message);
    
    /**
     * @brief 设置连接状态回调
     * 
     * @param callback 回调函数
     */
    void SetConnectionCallback(connection_callback_t callback);
    
    /**
     * @brief 设置数据接收回调
     * 
     * @param callback 回调函数
     */
    void SetDataCallback(data_callback_t callback);
    
    /**
     * @brief 设置状态消息回调
     * 
     * @param callback 回调函数
     */
    void SetStatusCallback(status_callback_t callback);
    
    /**
     * @brief 设置设备名称
     * 
     * @param name 设备名称
     */
    void SetDeviceName(const std::string& name);
    
    /**
     * @brief 设置目标设备名称
     * 
     * @param name 目标设备名称
     */
    void SetTargetDeviceName(const std::string& name);
    
    /**
     * @brief 获取连接状态
     * 
     * @return ConnectionState 连接状态
     */
    ConnectionState GetConnectionState() const { return connection_state_; }
    
    /**
     * @brief 是否正在扫描
     * 
     * @return true 正在扫描
     * @return false 未在扫描
     */
    bool IsScanning() const { return scanning_; }
    
    /**
     * @brief 获取发现的设备列表
     * 
     * @return const std::vector<DiscoveredDevice>& 设备列表
     */
    const std::vector<DiscoveredDevice>& GetDiscoveredDevices() const;
    
    /**
     * @brief 获取统计信息
     * 
     * @param packets_sent 发送包数
     * @param bytes_sent 发送字节数
     * @param connection_attempts 连接尝试次数
     */
    void GetStatistics(uint32_t& packets_sent, uint32_t& bytes_sent, uint32_t& connection_attempts) const;
    
    /**
     * @brief 重置统计信息
     */
    void ResetStatistics();

private:
    /**
     * @brief 构造函数（私有）
     */
    PicoBleClient();
    
    /**
     * @brief 析构函数（私有）
     */
    ~PicoBleClient();
    
    /**
     * @brief 初始化蓝牙控制器
     * 
     * @return true 成功
     * @return false 失败
     */
    bool InitializeBluetooth();
    
    /**
     * @brief 初始化BLE
     * 
     * @return true 成功
     * @return false 失败
     */
    bool InitializeBle();
    
    /**
     * @brief 设置扫描参数
     * 
     * @return true 成功
     * @return false 失败
     */
    bool SetupScanParams();
    
    /**
     * @brief GAP事件处理
     * 
     * @param event 事件类型
     * @param param 事件参数
     */
    void HandleGapEvent(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param);
    
    /**
     * @brief GATTC事件处理
     * 
     * @param event 事件类型
     * @param gattc_if GATT客户端接口
     * @param param 事件参数
     */
    void HandleGattcEvent(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_gattc_cb_param_t* param);
    
    /**
     * @brief 处理设备发现
     * 
     * @param param 扫描结果参数
     */
    void HandleDeviceDiscovered(esp_ble_gap_cb_param_t* param);
    
    /**
     * @brief 处理连接建立
     */
    void HandleConnectionEstablished();
    
    /**
     * @brief 处理连接断开
     */
    void HandleConnectionClosed();
    
    /**
     * @brief 处理接收到的数据
     * 
     * @param data 数据
     * @param length 数据长度
     */
    void HandleDataReceived(const uint8_t* data, size_t length);
    
    /**
     * @brief 发送状态消息
     * 
     * @param message 消息内容
     */
    void SendStatusMessage(const std::string& message);
    
    /**
     * @brief 静态GAP回调函数
     * 
     * @param event 事件类型
     * @param param 事件参数
     */
    static void StaticGapCallback(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t* param);
    
    /**
     * @brief 静态GATTC回调函数
     * 
     * @param event 事件类型
     * @param gattc_if GATT客户端接口
     * @param param 事件参数
     */
    static void StaticGattcCallback(esp_gattc_cb_event_t event, esp_gatt_if_t gattc_if, esp_gattc_cb_param_t* param);
};

#endif // PICO_BLE_CLIENT_H
