# 健康提醒音效使用指南

## 📋 概述

本文档介绍了新增的健康提醒音效功能，包括音效文件、配置和使用方法。

## 🎵 可用音效

### 基础健康提醒音效
- **`P3_HEALTH_GENTLE`** - 温和的健康提醒音
  - 用途：一般性健康提醒
  - 音效：温和的提示音
  
- **`P3_HEALTH_ATTENTION`** - 需要注意的健康提醒
  - 用途：需要用户注意的健康问题
  - 音效：引起注意的提示音
  
- **`P3_HEALTH_WARNING`** - 健康警告音
  - 用途：严重的健康警告
  - 音效：警告性提示音
  
- **`P3_HEALTH_SUCCESS`** - 健康目标达成音
  - 用途：健康目标完成或良好习惯确认
  - 音效：成功提示音

### 专项健康提醒音效
- **`P3_POSTURE_REMINDER`** - 姿势提醒音
  - 用途：坐姿不良提醒
  - 音效：激活音效
  
- **`P3_BREAK_REMINDER`** - 休息提醒音
  - 用途：工作休息提醒
  - 音效：温和提示音
  
- **`P3_EYE_CARE`** - 眼部护理提醒音
  - 用途：眼部疲劳提醒
  - 音效：欢迎音效
  
- **`P3_EMOTION_CARE`** - 情绪关怀音
  - 用途：情绪状态关怀
  - 音效：成功音效

## 🔧 使用方法

### 在代码中使用音效

```cpp
#include "assets/lang_config.h"

// 播放姿势提醒音
PlaySound(Lang::Sounds::P3_POSTURE_REMINDER);

// 播放情绪关怀音
PlaySoundImmediate(Lang::Sounds::P3_EMOTION_CARE);

// 播放健康成功音
audio_player->PlayFile(Lang::Sounds::P3_HEALTH_SUCCESS);
```

### 智能警报功能

智能警报功能会根据警报类型自动选择合适的音效：

```cpp
// 姿势警报
if (is_posture_alert) {
    alert_sound = Lang::Sounds::P3_POSTURE_REMINDER;
}
// 情绪警报
else if (is_emotion_alert) {
    if (emotion_state == "sad" || emotion_state == "tired") {
        alert_sound = Lang::Sounds::P3_EMOTION_CARE;
    } else {
        alert_sound = Lang::Sounds::P3_HEALTH_ATTENTION;
    }
}
// 一般健康提醒
else {
    alert_sound = Lang::Sounds::P3_HEALTH_GENTLE;
}
```

## 📁 文件结构

```
main/assets/zh-CN/
├── health_gentle.p3      # 温和健康提醒音
├── health_attention.p3   # 注意健康提醒音
├── health_warning.p3     # 健康警告音
├── health_success.p3     # 健康成功音
├── posture_reminder.p3   # 姿势提醒音
├── break_reminder.p3     # 休息提醒音
├── eye_care.p3          # 眼部护理音
└── emotion_care.p3      # 情绪关怀音
```

## 🛠️ 配置文件

音效常量在 `main/assets/lang_config.h` 中定义：

```cpp
namespace Lang {
    namespace Sounds {
        static const std::string_view P3_HEALTH_GENTLE;
        static const std::string_view P3_HEALTH_ATTENTION;
        static const std::string_view P3_HEALTH_WARNING;
        static const std::string_view P3_HEALTH_SUCCESS;
        static const std::string_view P3_POSTURE_REMINDER;
        static const std::string_view P3_BREAK_REMINDER;
        static const std::string_view P3_EYE_CARE;
        static const std::string_view P3_EMOTION_CARE;
    }
}
```

## 🔄 更新流程

如果需要添加新的健康提醒音效：

1. **添加音效文件**
   ```bash
   # 将新的P3文件复制到assets目录
   cp new_sound.p3 main/assets/zh-CN/
   ```

2. **更新语言配置**
   ```bash
   python scripts/gen_lang.py --input main/assets/zh-CN/language.json --output main/assets/lang_config.h
   ```

3. **在代码中使用**
   ```cpp
   PlaySound(Lang::Sounds::P3_NEW_SOUND);
   ```

4. **重新编译**
   ```bash
   idf.py build
   ```

## 🧪 测试

使用测试脚本验证配置：

```bash
python scripts/test_health_sounds.py
```

测试内容包括：
- ✅ 音效文件是否存在
- ✅ 语言配置是否正确
- ✅ 代码中是否正确使用

## 📝 健康提醒场景

### 姿势提醒
- **触发条件**：检测到不良坐姿
- **音效**：`P3_POSTURE_REMINDER`
- **消息**："请注意您的坐姿，保持背部挺直"

### 休息提醒
- **触发条件**：长时间工作
- **音效**：`P3_BREAK_REMINDER`
- **消息**："您已经工作很久了，建议休息一下"

### 眼部护理
- **触发条件**：长时间用眼
- **音效**：`P3_EYE_CARE`
- **消息**："请注意保护眼睛，适当远眺放松"

### 情绪关怀
- **触发条件**：检测到疲惫或负面情绪
- **音效**：`P3_EMOTION_CARE`
- **消息**："检测到您可能有些疲惫，请注意休息"

## 🎯 最佳实践

1. **音效选择**
   - 根据警报严重程度选择合适的音效
   - 避免过于刺耳的音效影响用户体验

2. **播放时机**
   - 在显示消息前播放音效
   - 给音效足够的播放时间

3. **用户体验**
   - 提供音效开关选项
   - 支持音量调节
   - 避免频繁播放相同音效

## 🔍 故障排除

### 音效不播放
1. 检查音效文件是否存在
2. 验证语言配置是否正确
3. 确认音频播放器是否正常工作

### 编译错误
1. 运行测试脚本检查配置
2. 重新生成语言配置文件
3. 清理并重新编译项目

### 音效质量问题
1. 检查原始音频文件质量
2. 验证P3转换是否正确
3. 调整音频参数重新转换

---

**注意**：本功能需要重新编译项目才能生效。确保在修改音效文件后运行完整的编译流程。
