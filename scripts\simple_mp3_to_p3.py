#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MP3到P3转换工具
使用ffmpeg直接转换为适合的音频格式
"""

import os
import subprocess
import shutil
from pathlib import Path

def check_ffmpeg():
    """
    检查ffmpeg是否可用
    """
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ffmpeg 可用")
            return True
        else:
            print("❌ ffmpeg 不可用")
            return False
    except FileNotFoundError:
        print("❌ 未找到 ffmpeg")
        print("请安装 ffmpeg: https://ffmpeg.org/download.html")
        return False

def convert_mp3_to_opus(mp3_file, opus_file):
    """
    使用ffmpeg将MP3转换为Opus格式
    """
    try:
        cmd = [
            "ffmpeg",
            "-i", str(mp3_file),
            "-c:a", "libopus",
            "-ar", "16000",      # 16kHz采样率
            "-ac", "1",          # 单声道
            "-b:a", "32k",       # 32kbps比特率
            "-frame_duration", "60",  # 60ms帧长度
            "-y",                # 覆盖输出文件
            str(opus_file)
        ]
        
        print(f"🔄 转换: {mp3_file.name} -> {opus_file.name}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 转换成功: {opus_file.name}")
            return True
        else:
            print(f"❌ 转换失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 转换错误: {e}")
        return False

def copy_as_p3(opus_file, p3_file):
    """
    将opus文件复制为p3格式
    """
    try:
        shutil.copy2(opus_file, p3_file)
        size = p3_file.stat().st_size
        print(f"✅ 创建P3文件: {p3_file.name} ({size} bytes)")
        return True
    except Exception as e:
        print(f"❌ 创建P3文件失败: {e}")
        return False

def convert_recorded_voices():
    """
    转换录制的语音文件
    """
    print("🎤 转换录制的语音文件...")
    
    temp_dir = Path("temp_voice")
    target_dir = Path("main/assets/zh-CN")
    opus_dir = Path("temp_opus")
    
    # 创建临时目录
    opus_dir.mkdir(exist_ok=True)
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 文件映射
    file_mapping = {
        "posture_bad.mp3": "health_posture_bad.p3",
        "emotion_sad.mp3": "health_emotion_sad.p3",
        "emotion_angry.mp3": "health_emotion_angry.p3",
        "emotion_happy.mp3": "health_emotion_happy.p3"
    }
    
    success_count = 0
    total_count = len(file_mapping)
    
    for mp3_name, p3_name in file_mapping.items():
        mp3_file = temp_dir / mp3_name
        opus_file = opus_dir / f"{mp3_file.stem}.opus"
        p3_file = target_dir / p3_name
        
        if not mp3_file.exists():
            print(f"❌ 源文件不存在: {mp3_file}")
            continue
        
        # 转换为opus
        if convert_mp3_to_opus(mp3_file, opus_file):
            # 复制为p3
            if copy_as_p3(opus_file, p3_file):
                success_count += 1
            else:
                print(f"❌ P3创建失败: {p3_name}")
        else:
            print(f"❌ Opus转换失败: {mp3_name}")
    
    # 清理临时文件
    if opus_dir.exists():
        shutil.rmtree(opus_dir)
        print("🧹 清理临时文件")
    
    print(f"\n📊 转换结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def verify_converted_files():
    """
    验证转换后的文件
    """
    print("\n🔍 验证转换后的文件...")
    
    target_dir = Path("main/assets/zh-CN")
    
    files_to_check = {
        "health_posture_bad.p3": "坐姿提醒",
        "health_emotion_sad.p3": "悲伤关怀",
        "health_emotion_angry.p3": "愤怒安抚",
        "health_emotion_happy.p3": "开心确认"
    }
    
    all_good = True
    for filename, description in files_to_check.items():
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            if size > 1000:  # 至少1KB
                print(f"✅ {filename}: {description} ({size} bytes)")
            else:
                print(f"⚠️ {filename}: {description} (文件较小: {size} bytes)")
        else:
            print(f"❌ {filename}: {description} (不存在)")
            all_good = False
    
    return all_good

def main():
    """
    主函数
    """
    print("🎵 简化MP3到P3转换工具")
    print("=" * 40)
    
    # 检查ffmpeg
    if not check_ffmpeg():
        print("\n❌ 需要安装 ffmpeg")
        print("请从 https://ffmpeg.org/download.html 下载安装")
        return
    
    # 转换文件
    if convert_recorded_voices():
        print("\n✅ 语音文件转换成功！")
        
        # 验证文件
        if verify_converted_files():
            print("\n🎉 所有文件验证通过！")
            print("\n🚀 下一步:")
            print("1. 编译项目: idf.py build")
            print("2. 烧录设备: idf.py flash")
            print("3. 测试健康警报")
            
            print("\n💡 预期效果:")
            print("- 坐姿不良 -> 播放您录制的坐姿提醒语音")
            print("- 悲伤情绪 -> 播放您录制的关怀语音")
            print("- 愤怒情绪 -> 播放您录制的安抚语音")
            print("- 开心情绪 -> 播放您录制的确认语音")
            
        else:
            print("\n⚠️ 部分文件验证失败")
    else:
        print("\n❌ 语音文件转换失败")

if __name__ == "__main__":
    main()
