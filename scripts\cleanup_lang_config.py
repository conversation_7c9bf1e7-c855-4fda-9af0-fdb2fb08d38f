#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理lang_config.h中错误的健康语音常量定义
"""

import re
from pathlib import Path

def cleanup_lang_config():
    """
    清理错误的常量定义
    """
    print("🧹 清理lang_config.h中的错误常量定义...")
    
    config_file = Path("main/assets/lang_config.h")
    if not config_file.exists():
        print(f"❌ 文件不存在: {config_file}")
        return False
    
    # 读取文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 要删除的常量模式
    patterns_to_remove = [
        r'extern const char p3_health_.*?_start\[\].*?\n',
        r'extern const char p3_health_.*?_end\[\].*?\n',
        r'static const std::string_view P3_HEALTH_.*?\{.*?\n.*?\n.*?\n.*?\};',
    ]
    
    # 删除匹配的模式
    original_content = content
    for pattern in patterns_to_remove:
        content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # 清理多余的空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    # 写回文件
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    if content != original_content:
        print("✅ 清理完成，删除了错误的健康语音常量定义")
        return True
    else:
        print("ℹ️ 没有找到需要清理的内容")
        return True

if __name__ == "__main__":
    cleanup_lang_config()
