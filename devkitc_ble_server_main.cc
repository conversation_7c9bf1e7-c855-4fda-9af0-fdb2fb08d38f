#include "esp_log.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "bluetooth/ble_server.h"
#include "cJSON.h"
#include <string>

static const char* TAG = "DevKitC_BLE_Server";

// 全局变量
static BleServer* g_ble_server = nullptr;
static QueueHandle_t g_data_queue = nullptr;

// 数据队列结构体
struct ReceivedDataPacket {
    char data[512];
    size_t length;
    char source_ip[16];
    uint16_t source_port;
    uint32_t timestamp;
    char received_time[32];
};

/**
 * @brief BLE数据接收回调函数
 */
static void ble_data_received_callback(const uint8_t* data, size_t length) {
    ESP_LOGI(TAG, "BLE data received: %d bytes", length);
    
    if (length > 0 && g_data_queue) {
        // 尝试解析JSON数据
        char* json_str = (char*)malloc(length + 1);
        if (json_str) {
            memcpy(json_str, data, length);
            json_str[length] = '\0';
            
            ESP_LOGI(TAG, "Received JSON: %s", json_str);
            
            cJSON* json = cJSON_Parse(json_str);
            if (json) {
                ReceivedDataPacket packet;
                memset(&packet, 0, sizeof(packet));
                
                // 解析JSON字段
                cJSON* data_item = cJSON_GetObjectItem(json, "data");
                cJSON* source_ip = cJSON_GetObjectItem(json, "source_ip");
                cJSON* source_port = cJSON_GetObjectItem(json, "source_port");
                cJSON* timestamp = cJSON_GetObjectItem(json, "timestamp");
                cJSON* data_length = cJSON_GetObjectItem(json, "length");
                
                if (cJSON_IsString(data_item)) {
                    strncpy(packet.data, data_item->valuestring, sizeof(packet.data) - 1);
                    packet.length = strlen(packet.data);
                }
                
                if (cJSON_IsString(source_ip)) {
                    strncpy(packet.source_ip, source_ip->valuestring, sizeof(packet.source_ip) - 1);
                }
                
                if (cJSON_IsNumber(source_port)) {
                    packet.source_port = source_port->valueint;
                }
                
                if (cJSON_IsNumber(timestamp)) {
                    packet.timestamp = timestamp->valueint;
                }
                
                // 添加接收时间戳
                time_t now;
                time(&now);
                struct tm* timeinfo = localtime(&now);
                strftime(packet.received_time, sizeof(packet.received_time), "%Y-%m-%d %H:%M:%S", timeinfo);
                
                // 发送到队列
                if (xQueueSend(g_data_queue, &packet, pdMS_TO_TICKS(100)) != pdTRUE) {
                    ESP_LOGW(TAG, "Failed to queue received data packet");
                } else {
                    ESP_LOGI(TAG, "Data packet queued successfully");
                }
                
                cJSON_Delete(json);
            } else {
                ESP_LOGW(TAG, "Failed to parse JSON data");
                // 如果不是JSON，直接作为原始数据处理
                ReceivedDataPacket packet;
                memset(&packet, 0, sizeof(packet));
                
                size_t copy_len = (length < sizeof(packet.data) - 1) ? length : sizeof(packet.data) - 1;
                memcpy(packet.data, data, copy_len);
                packet.data[copy_len] = '\0';
                packet.length = copy_len;
                
                // 添加接收时间戳
                time_t now;
                time(&now);
                struct tm* timeinfo = localtime(&now);
                strftime(packet.received_time, sizeof(packet.received_time), "%Y-%m-%d %H:%M:%S", timeinfo);
                
                if (xQueueSend(g_data_queue, &packet, pdMS_TO_TICKS(100)) != pdTRUE) {
                    ESP_LOGW(TAG, "Failed to queue raw data packet");
                }
            }
            
            free(json_str);
        }
    }
}

/**
 * @brief BLE连接状态回调函数
 */
static void ble_connection_callback(BleConnectionState state, const esp_bd_addr_t& address) {
    if (state == BLE_CONNECTED) {
        ESP_LOGI(TAG, "BLE client connected: %02x:%02x:%02x:%02x:%02x:%02x",
                 address[0], address[1], address[2], address[3], address[4], address[5]);
        ESP_LOGI(TAG, "Ready to receive data from PICO-1");
    } else if (state == BLE_DISCONNECTED) {
        ESP_LOGI(TAG, "BLE client disconnected");
        ESP_LOGI(TAG, "Waiting for new connection...");
    }
}

/**
 * @brief 数据处理任务
 */
static void data_processing_task(void* pvParameters) {
    ReceivedDataPacket packet;
    
    ESP_LOGI(TAG, "Data processing task started");
    
    while (true) {
        if (xQueueReceive(g_data_queue, &packet, portMAX_DELAY) == pdTRUE) {
            ESP_LOGI(TAG, "=== Processing WiFi Data ===");
            ESP_LOGI(TAG, "Received Time: %s", packet.received_time);
            ESP_LOGI(TAG, "Original Source: %s:%d", packet.source_ip, packet.source_port);
            ESP_LOGI(TAG, "Original Timestamp: %lu ms", packet.timestamp);
            ESP_LOGI(TAG, "Data Length: %d bytes", packet.length);
            ESP_LOGI(TAG, "Data Content: %s", packet.data);
            
            // 这里可以添加你的数据处理逻辑
            // 例如：
            // - 解析传感器数据
            // - 控制硬件设备
            // - 存储到数据库
            // - 转发到其他系统
            // - 触发警报或通知
            
            // 示例：检测特定数据类型
            if (strstr(packet.data, "emotion") != nullptr) {
                ESP_LOGI(TAG, "🎭 Emotion data detected - processing...");
                // 处理表情数据
            } else if (strstr(packet.data, "posture") != nullptr) {
                ESP_LOGI(TAG, "🪑 Posture data detected - processing...");
                // 处理坐姿数据
            } else if (strstr(packet.data, "temperature") != nullptr) {
                ESP_LOGI(TAG, "🌡️ Temperature data detected - processing...");
                // 处理温度数据
            } else if (strstr(packet.data, "humidity") != nullptr) {
                ESP_LOGI(TAG, "💧 Humidity data detected - processing...");
                // 处理湿度数据
            } else {
                ESP_LOGI(TAG, "📊 General data received - processing...");
                // 处理一般数据
            }
            
            ESP_LOGI(TAG, "========================");
        }
    }
}

/**
 * @brief 状态监控任务
 */
static void status_monitor_task(void* pvParameters) {
    uint32_t last_check_time = 0;
    
    while (true) {
        uint32_t current_time = esp_timer_get_time() / 1000000; // 秒
        
        // 每30秒显示一次状态
        if (current_time - last_check_time >= 30) {
            ESP_LOGI(TAG, "=== DevKitC BLE Server Status ===");
            
            if (g_ble_server) {
                BleConnectionState state = g_ble_server->GetConnectionState();
                const char* state_str = "Unknown";
                switch (state) {
                    case BLE_DISCONNECTED: state_str = "Disconnected"; break;
                    case BLE_CONNECTED: state_str = "Connected"; break;
                }
                ESP_LOGI(TAG, "BLE Server: %s", state_str);
                ESP_LOGI(TAG, "Advertising: %s", g_ble_server->IsAdvertising() ? "Yes" : "No");
                ESP_LOGI(TAG, "Device Name: %s", g_ble_server->GetDeviceName().c_str());
                
                if (g_ble_server->IsClientConnected()) {
                    auto addr = g_ble_server->GetConnectedDevice();
                    ESP_LOGI(TAG, "Connected Client: %02x:%02x:%02x:%02x:%02x:%02x",
                             addr[0], addr[1], addr[2], addr[3], addr[4], addr[5]);
                }
            }
            
            // 队列状态
            if (g_data_queue) {
                UBaseType_t queue_items = uxQueueMessagesWaiting(g_data_queue);
                ESP_LOGI(TAG, "Data queue: %d items pending", queue_items);
            }
            
            ESP_LOGI(TAG, "==============================");
            last_check_time = current_time;
        }
        
        vTaskDelay(pdMS_TO_TICKS(5000)); // 每5秒检查一次
    }
}

/**
 * @brief 心跳任务
 */
static void heartbeat_task(void* pvParameters) {
    while (true) {
        if (g_ble_server && g_ble_server->IsClientConnected()) {
            // 发送心跳消息给PICO
            std::string heartbeat = "DevKitC-Heartbeat:" + std::to_string(esp_timer_get_time() / 1000);
            g_ble_server->SendData((const uint8_t*)heartbeat.c_str(), heartbeat.length());
            ESP_LOGD(TAG, "Sent heartbeat to PICO");
        }
        
        vTaskDelay(pdMS_TO_TICKS(60000)); // 每60秒发送一次心跳
    }
}

/**
 * @brief 主函数
 */
extern "C" void app_main(void) {
    ESP_LOGI(TAG, "ESP32-S3 DevKitC-1 BLE Server Starting...");
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 创建数据队列
    g_data_queue = xQueueCreate(20, sizeof(ReceivedDataPacket));
    if (!g_data_queue) {
        ESP_LOGE(TAG, "Failed to create data queue");
        return;
    }
    
    // 获取BLE服务器实例
    g_ble_server = BleServer::GetInstance();
    if (!g_ble_server) {
        ESP_LOGE(TAG, "Failed to get BLE server instance");
        return;
    }
    
    // 设置设备名称
    g_ble_server->SetDeviceName("XiaoZhi-ESP32S3-DevKitC");
    
    // 设置回调函数
    g_ble_server->SetDataCallback(ble_data_received_callback);
    g_ble_server->SetConnectionCallback(ble_connection_callback);
    
    // 初始化BLE服务器
    if (!g_ble_server->Initialize()) {
        ESP_LOGE(TAG, "Failed to initialize BLE server");
        BleServer::DestroyInstance();
        return;
    }
    
    // 开始广播
    if (!g_ble_server->StartAdvertising()) {
        ESP_LOGE(TAG, "Failed to start advertising");
        return;
    }
    
    ESP_LOGI(TAG, "=== ESP32-S3 DevKitC-1 BLE Server Started ===");
    ESP_LOGI(TAG, "📱 Device Name: XiaoZhi-ESP32S3-DevKitC");
    ESP_LOGI(TAG, "📡 BLE Advertising: Active");
    ESP_LOGI(TAG, "🔄 Ready to receive data from PICO-1");
    ESP_LOGI(TAG, "============================================");
    
    // 创建任务
    xTaskCreate(data_processing_task, "data_processing", 6144, NULL, 6, NULL);
    xTaskCreate(status_monitor_task, "status_monitor", 3072, NULL, 3, NULL);
    xTaskCreate(heartbeat_task, "heartbeat", 2048, NULL, 2, NULL);
    
    ESP_LOGI(TAG, "All tasks created successfully");
    ESP_LOGI(TAG, "DevKitC-1 is ready to receive WiFi data from PICO-1");
    
    // 主循环
    while (true) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 这里可以添加其他周期性任务
        // 例如：看门狗喂狗、系统状态检查等
    }
    
    // 清理资源（通常不会执行到这里）
    if (g_ble_server) {
        g_ble_server->StopAdvertising();
        BleServer::DestroyInstance();
        g_ble_server = nullptr;
    }
    
    if (g_data_queue) {
        vQueueDelete(g_data_queue);
        g_data_queue = nullptr;
    }
}
