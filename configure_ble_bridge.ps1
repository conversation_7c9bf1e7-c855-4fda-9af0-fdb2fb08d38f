# ESP32-S3 WiFi-BLE桥接系统配置脚本
# 用于快速配置WiFi和BLE参数

param(
    [string]$WiFiSSID = "ESP32Test",
    [string]$WiFiPassword = "12345678",
    [string]$PicoDeviceName = "ESP32S3-PICO-Client",
    [string]$DevKitCDeviceName = "XiaoZhi-ESP32S3-DevKitC",
    [int]$WiFiPort = 8080,
    [switch]$Interactive = $false
)

$ErrorActionPreference = "Stop"

Write-Host "ESP32-S3 WiFi-BLE Bridge Configuration Tool" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# 配置文件路径
$ConfigFile = "ble_bridge_config.h"
$PicoMainFile = "main/pico_wifi_bt_main.cc"
$DevKitCMainFile = "devkitc_ble_server_main.cc"

# 检查文件是否存在
if (-not (Test-Path $ConfigFile)) {
    Write-Host "Error: Configuration file not found: $ConfigFile" -ForegroundColor Red
    exit 1
}

# 交互式配置
if ($Interactive -or [string]::IsNullOrEmpty($WiFiSSID)) {
    Write-Host ""
    Write-Host "Interactive Configuration Mode" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    
    if ([string]::IsNullOrEmpty($WiFiSSID)) {
        $WiFiSSID = Read-Host "Enter WiFi SSID"
    }
    
    if ([string]::IsNullOrEmpty($WiFiPassword)) {
        $WiFiPassword = Read-Host "Enter WiFi Password" -AsSecureString
        $WiFiPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($WiFiPassword))
    }
    
    $response = Read-Host "WiFi Port (default: $WiFiPort)"
    if (-not [string]::IsNullOrEmpty($response)) {
        $WiFiPort = [int]$response
    }
    
    $response = Read-Host "PICO Device Name (default: $PicoDeviceName)"
    if (-not [string]::IsNullOrEmpty($response)) {
        $PicoDeviceName = $response
    }
    
    $response = Read-Host "DevKitC Device Name (default: $DevKitCDeviceName)"
    if (-not [string]::IsNullOrEmpty($response)) {
        $DevKitCDeviceName = $response
    }
}

# 验证输入
if ([string]::IsNullOrEmpty($WiFiSSID)) {
    Write-Host "Error: WiFi SSID cannot be empty" -ForegroundColor Red
    exit 1
}

if ([string]::IsNullOrEmpty($WiFiPassword)) {
    Write-Host "Error: WiFi Password cannot be empty" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Configuration Summary:" -ForegroundColor Yellow
Write-Host "=====================" -ForegroundColor Yellow
Write-Host "WiFi SSID: $WiFiSSID" -ForegroundColor White
Write-Host "WiFi Password: $('*' * $WiFiPassword.Length)" -ForegroundColor White
Write-Host "WiFi Port: $WiFiPort" -ForegroundColor White
Write-Host "PICO Device Name: $PicoDeviceName" -ForegroundColor White
Write-Host "DevKitC Device Name: $DevKitCDeviceName" -ForegroundColor White

$confirm = Read-Host "`nApply these settings? (y/N)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Configuration cancelled." -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "Applying configuration..." -ForegroundColor Green

try {
    # 备份原始配置文件
    $BackupFile = "$ConfigFile.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $ConfigFile $BackupFile
    Write-Host "Backed up original config to: $BackupFile" -ForegroundColor Yellow
    
    # 读取配置文件内容
    $content = Get-Content $ConfigFile -Raw
    
    # 更新WiFi配置
    $content = $content -replace '#define WIFI_SSID\s+"[^"]*"', "#define WIFI_SSID                   `"$WiFiSSID`""
    $content = $content -replace '#define WIFI_PASSWORD\s+"[^"]*"', "#define WIFI_PASSWORD               `"$WiFiPassword`""
    $content = $content -replace '#define WIFI_DATA_RECEIVER_PORT\s+\d+', "#define WIFI_DATA_RECEIVER_PORT     $WiFiPort"
    
    # 更新BLE设备名称
    $content = $content -replace '#define BLE_DEVICE_NAME_PICO\s+"[^"]*"', "#define BLE_DEVICE_NAME_PICO        `"$PicoDeviceName`""
    $content = $content -replace '#define BLE_DEVICE_NAME_DEVKITC\s+"[^"]*"', "#define BLE_DEVICE_NAME_DEVKITC     `"$DevKitCDeviceName`""
    
    # 写入更新后的配置
    Set-Content $ConfigFile $content -NoNewline
    Write-Host "Updated configuration file: $ConfigFile" -ForegroundColor Green
    
    # 更新PICO主文件中的WiFi配置（如果存在硬编码）
    if (Test-Path $PicoMainFile) {
        $picoContent = Get-Content $PicoMainFile -Raw
        $picoContent = $picoContent -replace 'YOUR_WIFI_SSID', $WiFiSSID
        $picoContent = $picoContent -replace 'YOUR_WIFI_PASSWORD', $WiFiPassword
        Set-Content $PicoMainFile $picoContent -NoNewline
        Write-Host "Updated PICO main file: $PicoMainFile" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Configuration applied successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Build and flash PICO firmware:" -ForegroundColor White
    Write-Host "   .\build_pico_ble_client.ps1 -Action flash-monitor" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Build and flash DevKitC firmware:" -ForegroundColor White
    Write-Host "   .\build_devkitc_ble_server.ps1 -Action flash-monitor" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. Power on both devices and check serial output" -ForegroundColor White
    Write-Host ""
    Write-Host "WiFi Data Receiver will be available at:" -ForegroundColor Yellow
    Write-Host "  IP: [PICO_IP_ADDRESS]:$WiFiPort" -ForegroundColor White
    Write-Host ""
    Write-Host "BLE Connection:" -ForegroundColor Yellow
    Write-Host "  Client: $PicoDeviceName" -ForegroundColor White
    Write-Host "  Server: $DevKitCDeviceName" -ForegroundColor White
    
} catch {
    Write-Host "Error applying configuration: $($_.Exception.Message)" -ForegroundColor Red
    
    # 恢复备份
    if (Test-Path $BackupFile) {
        Copy-Item $BackupFile $ConfigFile -Force
        Write-Host "Restored original configuration from backup" -ForegroundColor Yellow
        Remove-Item $BackupFile -Force
    }
    
    exit 1
}

Write-Host ""
Write-Host "Configuration completed successfully!" -ForegroundColor Green
