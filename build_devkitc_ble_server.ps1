# ESP32-S3 DevKitC-1 BLE服务器构建脚本
# 用于编译DevKitC作为BLE服务器的固件

param(
    [string]$Action = "build",  # build, flash, monitor, clean
    [string]$Port = "COM4"      # 串口端口
)

$ErrorActionPreference = "Stop"

Write-Host "ESP32-S3 DevKitC-1 BLE Server Build Script" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# 检查ESP-IDF环境
if (-not $env:IDF_PATH) {
    Write-Host "Error: ESP-IDF environment not found. Please run setup_esp_idf_5.4.2.ps1 first." -ForegroundColor Red
    exit 1
}

# 设置项目配置
$PROJECT_NAME = "esp32_devkitc_ble_server"
$CMAKE_FILE = "CMakeLists_DevKitC.txt"
$MAIN_FILE = "devkitc_ble_server_main.cc"

Write-Host "Project: $PROJECT_NAME" -ForegroundColor Cyan
Write-Host "Target: ESP32-S3 DevKitC-1" -ForegroundColor Cyan
Write-Host "Function: BLE Server + Data Processing" -ForegroundColor Cyan
Write-Host ""

# 检查必要文件
$required_files = @(
    $CMAKE_FILE,
    $MAIN_FILE,
    "main/bluetooth/ble_server.h",
    "main/bluetooth/ble_server.cc"
)

foreach ($file in $required_files) {
    if (-not (Test-Path $file)) {
        Write-Host "Error: Required file not found: $file" -ForegroundColor Red
        exit 1
    }
}

# 备份原始文件
if (Test-Path "CMakeLists.txt") {
    Copy-Item "CMakeLists.txt" "CMakeLists.txt.backup" -Force
    Write-Host "Backed up original CMakeLists.txt" -ForegroundColor Yellow
}

if (Test-Path "main/main.cc") {
    Copy-Item "main/main.cc" "main/main.cc.backup" -Force
    Write-Host "Backed up original main.cc" -ForegroundColor Yellow
}

# 使用DevKitC专用的配置文件
Copy-Item $CMAKE_FILE "CMakeLists.txt" -Force
Copy-Item $MAIN_FILE "main/main.cc" -Force
Write-Host "Using DevKitC-specific configuration files" -ForegroundColor Green

try {
    switch ($Action.ToLower()) {
        "build" {
            Write-Host "Building DevKitC BLE Server firmware..." -ForegroundColor Yellow
            idf.py build
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Build completed successfully!" -ForegroundColor Green
                Write-Host ""
                Write-Host "Firmware ready for ESP32-S3 DevKitC-1" -ForegroundColor Cyan
                Write-Host "Features:" -ForegroundColor Cyan
                Write-Host "  - BLE Server (Device: XiaoZhi-ESP32S3-DevKitC)" -ForegroundColor White
                Write-Host "  - Data Reception from PICO-1" -ForegroundColor White
                Write-Host "  - JSON Data Processing" -ForegroundColor White
                Write-Host "  - Real-time Status Monitoring" -ForegroundColor White
            } else {
                Write-Host "Build failed!" -ForegroundColor Red
                exit 1
            }
        }
        
        "flash" {
            Write-Host "Flashing DevKitC BLE Server firmware to $Port..." -ForegroundColor Yellow
            idf.py -p $Port flash
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Flash completed successfully!" -ForegroundColor Green
            } else {
                Write-Host "Flash failed!" -ForegroundColor Red
                exit 1
            }
        }
        
        "monitor" {
            Write-Host "Starting serial monitor on $Port..." -ForegroundColor Yellow
            Write-Host "Press Ctrl+] to exit monitor" -ForegroundColor Cyan
            idf.py -p $Port monitor
        }
        
        "flash-monitor" {
            Write-Host "Flashing and monitoring DevKitC BLE Server..." -ForegroundColor Yellow
            idf.py -p $Port flash monitor
        }
        
        "clean" {
            Write-Host "Cleaning build files..." -ForegroundColor Yellow
            idf.py clean
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Clean completed!" -ForegroundColor Green
            }
        }
        
        "menuconfig" {
            Write-Host "Opening configuration menu..." -ForegroundColor Yellow
            idf.py menuconfig
        }
        
        default {
            Write-Host "Usage: .\build_devkitc_ble_server.ps1 [-Action <action>] [-Port <port>]" -ForegroundColor Yellow
            Write-Host ""
            Write-Host "Actions:" -ForegroundColor Cyan
            Write-Host "  build         - Build the firmware" -ForegroundColor White
            Write-Host "  flash         - Flash to device" -ForegroundColor White
            Write-Host "  monitor       - Start serial monitor" -ForegroundColor White
            Write-Host "  flash-monitor - Flash and monitor" -ForegroundColor White
            Write-Host "  clean         - Clean build files" -ForegroundColor White
            Write-Host "  menuconfig    - Open configuration menu" -ForegroundColor White
            Write-Host ""
            Write-Host "Examples:" -ForegroundColor Cyan
            Write-Host "  .\build_devkitc_ble_server.ps1 -Action build" -ForegroundColor White
            Write-Host "  .\build_devkitc_ble_server.ps1 -Action flash -Port COM5" -ForegroundColor White
            Write-Host "  .\build_devkitc_ble_server.ps1 -Action flash-monitor" -ForegroundColor White
        }
    }
} finally {
    # 恢复原始文件
    if (Test-Path "CMakeLists.txt.backup") {
        Move-Item "CMakeLists.txt.backup" "CMakeLists.txt" -Force
        Write-Host "Restored original CMakeLists.txt" -ForegroundColor Yellow
    }
    
    if (Test-Path "main/main.cc.backup") {
        Move-Item "main/main.cc.backup" "main/main.cc" -Force
        Write-Host "Restored original main.cc" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "DevKitC BLE Server build script completed." -ForegroundColor Green
