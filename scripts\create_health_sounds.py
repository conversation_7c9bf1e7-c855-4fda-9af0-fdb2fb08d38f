#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建健康提醒音效 - 简化版
复制现有的音效文件并重命名为健康提醒音效
"""

import os
import shutil
from pathlib import Path

def copy_and_rename_sounds():
    """
    复制现有音效文件并重命名为健康提醒音效
    """
    # 源音效文件路径
    assets_dir = Path("main/assets")
    zh_cn_dir = assets_dir / "zh-CN"
    common_dir = assets_dir / "common"
    
    # 目标目录
    output_dir = Path("health_sounds")
    output_dir.mkdir(exist_ok=True)
    
    # 健康提醒音效映射
    health_sounds = {
        # 使用现有音效文件创建健康提醒音效
        "health_gentle": ("common/popup.p3", "温和的健康提醒音"),
        "health_attention": ("common/exclamation.p3", "需要注意的健康提醒"),
        "health_warning": ("common/vibration.p3", "健康警告音"),
        "health_success": ("common/success.p3", "健康目标达成音"),
        "posture_reminder": ("zh-CN/activation.p3", "姿势提醒音"),
        "break_reminder": ("common/popup.p3", "休息提醒音"),
        "eye_care": ("zh-CN/welcome.p3", "眼部护理提醒音"),
        "emotion_care": ("common/success.p3", "情绪关怀音"),
    }
    
    print("开始创建健康提醒音效...")
    print(f"输出目录: {output_dir.absolute()}")
    
    success_count = 0
    total_count = len(health_sounds)
    
    for new_name, (source_file, description) in health_sounds.items():
        source_path = assets_dir / source_file
        target_path = output_dir / f"{new_name}.p3"
        
        print(f"\n创建: {new_name} - {description}")
        print(f"源文件: {source_path}")
        print(f"目标文件: {target_path}")
        
        if source_path.exists():
            try:
                shutil.copy2(source_path, target_path)
                success_count += 1
                print(f"✅ 成功创建: {new_name}")
            except Exception as e:
                print(f"❌ 复制失败: {new_name} - {e}")
        else:
            print(f"❌ 源文件不存在: {source_path}")
    
    print(f"\n生成完成！成功: {success_count}/{total_count}")
    print(f"音效文件位置: {output_dir.absolute()}")
    
    if success_count > 0:
        print("\n下一步:")
        print("1. 将P3文件复制到 main/assets/zh-CN/ 目录")
        print("2. 运行 python scripts/gen_lang.py 更新语言配置")
        print("3. 在代码中使用新的音效常量")
        print("\n可用的音效常量:")
        for filename in health_sounds.keys():
            constant_name = f"P3_{filename.upper()}"
            print(f"  Lang::Sounds::{constant_name}")
    
    return output_dir, success_count > 0

def copy_to_assets(source_dir):
    """
    将生成的音效文件复制到assets目录
    """
    assets_zh_cn = Path("main/assets/zh-CN")
    
    if not assets_zh_cn.exists():
        print(f"错误: 目标目录不存在: {assets_zh_cn}")
        return False
    
    print(f"\n将音效文件复制到: {assets_zh_cn}")
    
    copied_count = 0
    for p3_file in source_dir.glob("*.p3"):
        target_file = assets_zh_cn / p3_file.name
        try:
            shutil.copy2(p3_file, target_file)
            print(f"✅ 已复制: {p3_file.name}")
            copied_count += 1
        except Exception as e:
            print(f"❌ 复制失败: {p3_file.name} - {e}")
    
    print(f"复制完成！成功复制 {copied_count} 个文件")
    return copied_count > 0

def update_language_config():
    """
    更新语言配置
    """
    try:
        import subprocess
        import sys
        
        script_path = Path("scripts/gen_lang.py")
        if not script_path.exists():
            print("错误: gen_lang.py 脚本不存在")
            return False
        
        print("\n更新语言配置...")
        result = subprocess.run([sys.executable, str(script_path)], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 语言配置更新成功")
            return True
        else:
            print(f"❌ 语言配置更新失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 更新语言配置时出错: {e}")
        return False

def main():
    """
    主函数
    """
    print("🎵 健康提醒音效生成器")
    print("=" * 50)
    
    # 1. 创建健康音效文件
    output_dir, success = copy_and_rename_sounds()
    
    if not success:
        print("❌ 音效文件创建失败")
        return
    
    # 2. 询问是否复制到assets目录
    response = input("\n是否将音效文件复制到 main/assets/zh-CN/ 目录？(y/n): ")
    if response.lower() in ['y', 'yes', '是']:
        if copy_to_assets(output_dir):
            # 3. 询问是否更新语言配置
            response = input("\n是否更新语言配置？(y/n): ")
            if response.lower() in ['y', 'yes', '是']:
                update_language_config()
                print("\n🎉 所有步骤完成！")
                print("现在可以重新编译项目并使用新的健康提醒音效了。")
            else:
                print("\n请手动运行: python scripts/gen_lang.py")
        else:
            print("❌ 复制到assets目录失败")
    else:
        print(f"\n请手动将文件从 {output_dir} 复制到 main/assets/zh-CN/")

if __name__ == "__main__":
    main()
