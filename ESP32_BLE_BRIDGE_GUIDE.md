# ESP32-S3 WiFi-BLE桥接系统

## 项目概述

本项目实现了ESP32-S3 PICO-1和ESP32-S3 DevKitC-1之间的WiFi到BLE数据桥接功能，让PICO-1作为BLE客户端将WiFi接收的数据通过BLE传输给DevKitC-1服务器端进行处理。

### 系统架构
- **ESP32-S3 PICO-1**: BLE客户端，接收WiFi数据并通过BLE转发
- **ESP32-S3 DevKitC-1**: BLE服务器，接收来自PICO的数据并进行处理

### 主要特性
- ✅ WiFi数据接收 (端口8080)
- ✅ BLE客户端/服务器通信
- ✅ JSON数据格式支持
- ✅ 自动重连机制
- ✅ 实时状态监控
- ✅ 数据队列管理
- ✅ 错误处理和恢复

## 系统架构

```
[WiFi数据源] → [PICO-1 WiFi接收器] → [BLE传输] → [DevKitC-1 BLE服务器] → [数据处理]
```

## 硬件要求

### ESP32-S3 PICO-1 (BLE客户端)
- ESP32-S3-PICO-1开发板
- WiFi连接能力
- BLE功能

### ESP32-S3 DevKitC-1 (BLE服务器)  
- ESP32-S3-DevKitC-1开发板
- BLE功能

## 软件配置

### 1. PICO-1 配置 (BLE客户端)

#### WiFi配置
在 `main/pico_wifi_bt_main.cc` 中修改WiFi配置：

```cpp
#define WIFI_SSID      "YOUR_WIFI_SSID"      // 修改为你的WiFi名称
#define WIFI_PASS      "YOUR_WIFI_PASSWORD"  // 修改为你的WiFi密码
```

#### BLE目标设备配置
```cpp
#define TARGET_DEVICE_NAME "XiaoZhi-ESP32S3-DevKitC"  // DevKitC的BLE设备名称
```

### 2. DevKitC-1 配置 (BLE服务器)

BLE设备名称在 `devkitc_ble_server_main.cc` 中设置：
```cpp
g_ble_server->SetDeviceName("XiaoZhi-ESP32S3-DevKitC");
```

## 快速开始

### 1. 环境准备
确保ESP-IDF 5.4.2已正确安装并运行环境设置脚本：
```powershell
.\setup_esp_idf_5.4.2.ps1
```

### 2. 快速配置
使用配置脚本快速设置WiFi和BLE参数：
```powershell
# 交互式配置
.\configure_ble_bridge.ps1 -Interactive

# 或直接指定参数
.\configure_ble_bridge.ps1 -WiFiSSID "YourWiFi" -WiFiPassword "YourPassword"
```

## 编译和烧录

### 编译PICO-1固件 (BLE客户端)
```powershell
# 编译
.\build_pico_ble_client.ps1 -Action build

# 烧录 (修改COM端口)
.\build_pico_ble_client.ps1 -Action flash -Port COM3

# 烧录并监控
.\build_pico_ble_client.ps1 -Action flash-monitor -Port COM3
```

### 编译DevKitC-1固件 (BLE服务器)
```powershell
# 编译
.\build_devkitc_ble_server.ps1 -Action build

# 烧录 (修改COM端口)
.\build_devkitc_ble_server.ps1 -Action flash -Port COM4

# 烧录并监控
.\build_devkitc_ble_server.ps1 -Action flash-monitor -Port COM4
```

## 系统启动流程

### 1. 启动DevKitC-1 (BLE服务器)
1. 上电后自动初始化BLE服务器
2. 开始BLE广播，设备名称：`XiaoZhi-ESP32S3-DevKitC`
3. 等待PICO-1连接

### 2. 启动PICO-1 (BLE客户端)
1. 连接到配置的WiFi网络
2. 启动WiFi数据接收器 (端口8080)
3. 开始扫描BLE设备
4. 自动连接到DevKitC-1
5. 开始数据转发

## 数据流程

### WiFi数据接收
PICO-1在端口8080监听WiFi数据，支持的数据格式：
- JSON格式数据
- 原始文本数据
- 传感器数据

### BLE数据传输
数据通过BLE以JSON格式传输：
```json
{
  "data": "原始数据内容",
  "source_ip": "*************",
  "source_port": 12345,
  "timestamp": 1234567890,
  "length": 256
}
```

### DevKitC数据处理
DevKitC接收到数据后会：
1. 解析JSON格式
2. 提取原始数据和元信息
3. 根据数据类型进行分类处理
4. 记录接收时间戳

## 监控和调试

### 串口监控
```powershell
# 监控PICO-1
idf.py -p COM3 monitor

# 监控DevKitC-1  
idf.py -p COM4 monitor
```

### 日志信息
系统会输出详细的日志信息，包括：
- WiFi连接状态
- BLE连接状态
- 数据传输统计
- 错误信息

## 状态指示

### PICO-1状态
- WiFi连接成功：显示IP地址
- BLE扫描：显示发现的设备
- BLE连接：显示连接状态
- 数据转发：显示转发统计

### DevKitC-1状态
- BLE服务器启动：显示设备名称
- 客户端连接：显示连接的设备地址
- 数据接收：显示接收的数据内容
- 处理状态：显示数据处理结果

## 故障排除

### 常见问题

1. **WiFi连接失败**
   - 检查SSID和密码配置
   - 确认WiFi网络可用
   - 检查信号强度

2. **BLE连接失败**
   - 确认两个设备都已正确烧录固件
   - 检查设备名称配置是否匹配
   - 重启设备重新尝试连接

3. **数据传输异常**
   - 检查BLE连接状态
   - 查看串口日志错误信息
   - 确认数据格式正确

### 调试技巧

1. **使用串口监控**
   ```powershell
   idf.py -p COMx monitor
   ```

2. **查看详细日志**
   - 日志级别设置为INFO或DEBUG
   - 关注错误和警告信息

3. **重置设备**
   - 按下RST按钮重启设备
   - 重新建立连接

## 扩展功能

### 数据处理扩展
在DevKitC的 `data_processing_task` 中可以添加：
- 数据存储到SD卡
- 数据转发到云服务器
- 硬件控制逻辑
- 警报和通知功能

### 通信协议扩展
- 支持更多数据格式
- 添加数据压缩
- 实现数据加密
- 添加错误重传机制

## 性能参数

- **WiFi数据接收速率**: 取决于网络条件
- **BLE传输速率**: 约20KB/s
- **数据队列容量**: 20个数据包
- **连接超时**: 30秒
- **心跳间隔**: 60秒

## 注意事项

1. 确保两个设备使用相同的BLE配置
2. WiFi和BLE不能同时使用相同的天线（如果共享）
3. 注意功耗管理，特别是在电池供电时
4. 定期检查内存使用情况，避免内存泄漏
5. 在生产环境中建议添加看门狗机制

## 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 固件版本是否匹配
3. 配置参数是否正确
4. 串口日志中的错误信息
