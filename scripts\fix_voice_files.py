#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复语音文件问题
使用正确的P3格式创建健康提醒语音文件
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_dependencies():
    """
    检查依赖项
    """
    print("🔍 检查依赖项...")
    
    required_packages = [
        "librosa",
        "opuslib", 
        "numpy",
        "tqdm",
        "pyloudnorm"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请安装: pip install " + " ".join(missing_packages))
        return False
    
    return True

def create_health_voice_texts():
    """
    创建健康提醒语音文本
    """
    texts = {
        "health_posture_bad": "我发现你的坐姿不太好，记得挺直腰背哦！",
        "health_posture_good": "你的坐姿很端正，继续保持！", 
        "health_emotion_sad": "我注意到你最近看起来有些难过，要不要聊聊天？",
        "health_emotion_angry": "你看起来有点生气，深呼吸一下，放松心情吧。",
        "health_emotion_happy": "你看起来心情很好呢，真棒！",
        "health_gentle_reminder": "小智提醒您注意健康哦。",
        "health_success_confirm": "很好，继续保持健康的生活习惯！",
        "health_break_reminder": "你已经工作很久了，该休息一下了。",
        "health_eye_care": "记得让眼睛休息一下，看看远处的风景吧。"
    }
    
    return texts

def create_tts_audio_with_edge_tts(text, output_file):
    """
    使用edge-tts创建音频文件
    """
    try:
        cmd = [
            "edge-tts",
            "--voice", "zh-CN-XiaoxiaoNeural",
            "--rate", "-30%",  # 慢速
            "--pitch", "-10Hz", # 稍低音调
            "--text", text,
            "--write-media", output_file
        ]
        
        print(f"🎤 生成TTS音频: {text[:20]}...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"❌ TTS生成失败: {result.stderr}")
            return False
            
        return True
        
    except FileNotFoundError:
        print("❌ 未找到edge-tts，请安装: pip install edge-tts")
        return False
    except Exception as e:
        print(f"❌ TTS生成错误: {e}")
        return False

def convert_audio_to_p3(audio_file, p3_file):
    """
    使用正确的P3转换工具
    """
    try:
        script_path = Path("scripts/p3_tools/convert_audio_to_p3.py")
        if not script_path.exists():
            print(f"❌ P3转换脚本不存在: {script_path}")
            return False
        
        cmd = [
            sys.executable,
            str(script_path),
            str(audio_file),
            str(p3_file),
            "-d"  # 禁用响度标准化，因为TTS音频已经标准化
        ]
        
        print(f"🔄 转换为P3格式: {audio_file.name}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ P3转换失败: {result.stderr}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ P3转换错误: {e}")
        return False

def create_simple_text_to_p3(text, p3_file):
    """
    直接创建简单的P3文件（如果TTS不可用）
    """
    try:
        import librosa
        import opuslib
        import struct
        import numpy as np
        
        # 创建简单的提示音（正弦波）
        sample_rate = 16000
        duration = 2.0  # 2秒
        frequency = 800  # 800Hz提示音
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        # 创建渐变的正弦波
        audio = np.sin(2 * np.pi * frequency * t) * np.exp(-t * 2)  # 衰减
        audio = (audio * 0.3 * 32767).astype(np.int16)  # 降低音量
        
        # 初始化Opus编码器
        encoder = opuslib.Encoder(sample_rate, 1, opuslib.APPLICATION_AUDIO)
        
        # 编码并保存
        with open(p3_file, 'wb') as f:
            frame_duration = 60  # 60ms per frame
            frame_size = int(sample_rate * frame_duration / 1000)
            
            for i in range(0, len(audio) - frame_size, frame_size):
                frame = audio[i:i + frame_size]
                opus_data = encoder.encode(frame.tobytes(), frame_size=frame_size)
                packet = struct.pack('>BBH', 0, 0, len(opus_data)) + opus_data
                f.write(packet)
        
        print(f"✅ 创建简单P3文件: {p3_file.name}")
        return True
        
    except Exception as e:
        print(f"❌ 创建P3文件失败: {e}")
        return False

def remove_old_demo_files():
    """
    删除之前错误创建的演示文件
    """
    print("🧹 清理旧的演示文件...")
    
    target_dir = Path("main/assets/zh-CN")
    demo_files = [
        "health_posture_bad.p3",
        "health_posture_good.p3",
        "health_emotion_sad.p3", 
        "health_emotion_angry.p3",
        "health_emotion_happy.p3",
        "health_gentle_reminder.p3",
        "health_success_confirm.p3",
        "health_break_reminder.p3",
        "health_eye_care.p3"
    ]
    
    removed_count = 0
    for filename in demo_files:
        file_path = target_dir / filename
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"🗑️ 删除: {filename}")
                removed_count += 1
            except Exception as e:
                print(f"❌ 删除失败 {filename}: {e}")
    
    print(f"✅ 清理完成，删除了 {removed_count} 个文件")

def main():
    """
    主函数
    """
    print("🔧 修复小智健康提醒语音文件")
    print("=" * 50)
    
    # 清理旧文件
    remove_old_demo_files()
    
    # 检查依赖
    if not check_dependencies():
        print("\n💡 解决方案:")
        print("1. 安装缺失的依赖包")
        print("2. 或者使用简单模式创建提示音")
        
        use_simple = input("\n是否使用简单模式创建提示音? (y/n): ").lower().strip()
        if use_simple != 'y':
            return
    
    # 创建目录
    temp_dir = Path("temp_audio")
    target_dir = Path("main/assets/zh-CN")
    
    temp_dir.mkdir(exist_ok=True)
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取语音文本
    texts = create_health_voice_texts()
    
    print(f"\n🎵 开始创建 {len(texts)} 个语音文件...")
    
    success_count = 0
    
    for key, text in texts.items():
        print(f"\n🔄 处理: {key}")
        print(f"   内容: {text}")
        
        audio_file = temp_dir / f"{key}.mp3"
        p3_file = target_dir / f"{key}.p3"
        
        # 尝试使用TTS创建音频
        tts_success = False
        try:
            if create_tts_audio_with_edge_tts(text, str(audio_file)):
                if convert_audio_to_p3(audio_file, p3_file):
                    tts_success = True
                    success_count += 1
                    print(f"✅ {key} TTS语音创建成功")
        except Exception as e:
            print(f"⚠️ TTS创建失败: {e}")
        
        # 如果TTS失败，创建简单提示音
        if not tts_success:
            print(f"🔄 为 {key} 创建简单提示音...")
            if create_simple_text_to_p3(text, p3_file):
                success_count += 1
                print(f"✅ {key} 简单提示音创建成功")
            else:
                print(f"❌ {key} 创建失败")
    
    print(f"\n📊 创建结果: {success_count}/{len(texts)} 成功")
    
    if success_count > 0:
        print("\n🎯 下一步:")
        print("1. 编译项目: idf.py build")
        print("2. 烧录测试: idf.py flash")
        print("3. 测试语音效果")
        
        print("\n📁 创建的文件:")
        for key in texts.keys():
            p3_file = target_dir / f"{key}.p3"
            if p3_file.exists():
                size = p3_file.stat().st_size
                print(f"  ✅ {key}.p3 ({size} bytes)")
    
    # 清理临时文件
    if temp_dir.exists():
        import shutil
        shutil.rmtree(temp_dir)
        print("\n🧹 清理临时文件")
    
    print("\n💡 说明:")
    print("- 如果听到的是提示音而不是语音，说明TTS不可用")
    print("- 提示音可以验证系统工作正常")
    print("- 后续可以录制真实语音替换这些文件")

if __name__ == "__main__":
    main()
