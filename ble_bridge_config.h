#ifndef BLE_BRIDGE_CONFIG_H
#define BLE_BRIDGE_CONFIG_H

// ============================================================================
// ESP32-S3 WiFi-BLE桥接系统配置文件
// ============================================================================

// ----------------------------------------------------------------------------
// WiFi配置 (PICO-1使用)
// ----------------------------------------------------------------------------
#define WIFI_SSID                   "YOUR_WIFI_SSID"        // WiFi网络名称
#define WIFI_PASSWORD               "YOUR_WIFI_PASSWORD"    // WiFi密码
#define WIFI_MAXIMUM_RETRY          5                       // WiFi连接最大重试次数
#define WIFI_CONNECT_TIMEOUT_MS     30000                   // WiFi连接超时时间(毫秒)

// WiFi数据接收器配置
#define WIFI_DATA_RECEIVER_PORT     8080                    // WiFi数据接收端口
#define WIFI_DATA_MAX_CLIENTS       3                       // 最大客户端连接数
#define WIFI_DATA_RECV_TIMEOUT_MS   10000                   // 接收超时时间
#define WIFI_DATA_BUFFER_SIZE       512                     // 数据缓冲区大小

// ----------------------------------------------------------------------------
// BLE配置 (两个设备共用)
// ----------------------------------------------------------------------------
#define BLE_DEVICE_NAME_PICO        "ESP32S3-PICO-Client"           // PICO设备名称
#define BLE_DEVICE_NAME_DEVKITC     "XiaoZhi-ESP32S3-DevKitC"      // DevKitC设备名称
#define BLE_SCAN_DURATION_SEC       30                              // BLE扫描持续时间
#define BLE_CONNECTION_TIMEOUT_SEC  30                              // BLE连接超时时间
#define BLE_MTU_SIZE                512                             // BLE MTU大小
#define BLE_MAX_DATA_LENGTH         480                             // BLE最大数据长度

// BLE服务和特征UUID (16位UUID)
#define BLE_SERVICE_UUID            0x00FF                          // 服务UUID
#define BLE_CHARACTERISTIC_UUID     0xFF01                          // 特征UUID
#define BLE_DESCRIPTOR_UUID         0x2902                          // 描述符UUID

// ----------------------------------------------------------------------------
// 数据队列配置
// ----------------------------------------------------------------------------
#define DATA_QUEUE_SIZE             20                              // 数据队列大小
#define DATA_QUEUE_TIMEOUT_MS       100                             // 队列操作超时时间
#define DATA_PACKET_MAX_SIZE        512                             // 数据包最大大小

// ----------------------------------------------------------------------------
// 任务配置
// ----------------------------------------------------------------------------
// 任务堆栈大小
#define TASK_STACK_SIZE_WIFI_FORWARD    6144                       // WiFi数据转发任务
#define TASK_STACK_SIZE_BLE_SCAN        4096                       // BLE扫描连接任务
#define TASK_STACK_SIZE_DATA_PROCESS    6144                       // 数据处理任务
#define TASK_STACK_SIZE_STATUS_MONITOR  3072                       // 状态监控任务
#define TASK_STACK_SIZE_HEARTBEAT       2048                       // 心跳任务

// 任务优先级
#define TASK_PRIORITY_WIFI_FORWARD      6                          // WiFi数据转发任务优先级
#define TASK_PRIORITY_BLE_SCAN          5                          // BLE扫描连接任务优先级
#define TASK_PRIORITY_DATA_PROCESS      6                          // 数据处理任务优先级
#define TASK_PRIORITY_STATUS_MONITOR    3                          // 状态监控任务优先级
#define TASK_PRIORITY_HEARTBEAT         2                          // 心跳任务优先级

// ----------------------------------------------------------------------------
// 定时器配置
// ----------------------------------------------------------------------------
#define STATUS_REPORT_INTERVAL_SEC      30                         // 状态报告间隔
#define HEARTBEAT_INTERVAL_SEC          60                         // 心跳间隔
#define BLE_RECONNECT_INTERVAL_SEC      10                         // BLE重连间隔
#define WIFI_RECONNECT_INTERVAL_SEC     5                          // WiFi重连间隔

// ----------------------------------------------------------------------------
// 日志配置
// ----------------------------------------------------------------------------
#define LOG_LEVEL_WIFI                  ESP_LOG_INFO               // WiFi日志级别
#define LOG_LEVEL_BLE                   ESP_LOG_INFO               // BLE日志级别
#define LOG_LEVEL_DATA                  ESP_LOG_INFO               // 数据处理日志级别
#define LOG_LEVEL_SYSTEM                ESP_LOG_INFO               // 系统日志级别

// 是否启用详细日志
#define ENABLE_VERBOSE_LOGGING          1                          // 1=启用, 0=禁用
#define ENABLE_HEX_DUMP                 1                          // 1=启用十六进制转储, 0=禁用

// ----------------------------------------------------------------------------
// 功能开关
// ----------------------------------------------------------------------------
#define ENABLE_AUTO_RECONNECT           1                          // 自动重连功能
#define ENABLE_HEARTBEAT                1                          // 心跳功能
#define ENABLE_STATUS_MONITOR           1                          // 状态监控功能
#define ENABLE_DATA_VALIDATION          1                          // 数据验证功能
#define ENABLE_JSON_PARSING             1                          // JSON解析功能

// ----------------------------------------------------------------------------
// 性能配置
// ----------------------------------------------------------------------------
#define MAX_CONCURRENT_CONNECTIONS      1                          // 最大并发BLE连接数
#define DATA_PROCESSING_BATCH_SIZE      5                          // 数据处理批次大小
#define MEMORY_POOL_SIZE                8192                       // 内存池大小
#define WATCHDOG_TIMEOUT_SEC            60                         // 看门狗超时时间

// ----------------------------------------------------------------------------
// 错误处理配置
// ----------------------------------------------------------------------------
#define MAX_RETRY_ATTEMPTS              3                          // 最大重试次数
#define ERROR_RECOVERY_DELAY_MS         1000                       // 错误恢复延迟
#define CRITICAL_ERROR_REBOOT           1                          // 严重错误时重启

// ----------------------------------------------------------------------------
// 调试配置
// ----------------------------------------------------------------------------
#ifdef CONFIG_DEBUG_MODE
    #define DEBUG_PRINT_ENABLED         1                          // 调试打印
    #define DEBUG_ASSERT_ENABLED        1                          // 调试断言
    #define DEBUG_MEMORY_CHECK          1                          // 内存检查
#else
    #define DEBUG_PRINT_ENABLED         0
    #define DEBUG_ASSERT_ENABLED        0
    #define DEBUG_MEMORY_CHECK          0
#endif

// ----------------------------------------------------------------------------
// 硬件相关配置
// ----------------------------------------------------------------------------
// LED指示灯配置 (如果有)
#define LED_STATUS_PIN                  GPIO_NUM_2                 // 状态LED引脚
#define LED_WIFI_PIN                    GPIO_NUM_4                 // WiFi状态LED引脚
#define LED_BLE_PIN                     GPIO_NUM_5                 // BLE状态LED引脚

// 按键配置 (如果有)
#define BUTTON_RESET_PIN                GPIO_NUM_0                 // 复位按键引脚
#define BUTTON_CONFIG_PIN               GPIO_NUM_9                 // 配置按键引脚

// ----------------------------------------------------------------------------
// 数据格式配置
// ----------------------------------------------------------------------------
// JSON字段名称
#define JSON_FIELD_DATA                 "data"                     // 数据字段
#define JSON_FIELD_SOURCE_IP            "source_ip"                // 源IP字段
#define JSON_FIELD_SOURCE_PORT          "source_port"              // 源端口字段
#define JSON_FIELD_TIMESTAMP            "timestamp"                // 时间戳字段
#define JSON_FIELD_LENGTH               "length"                   // 长度字段
#define JSON_FIELD_TYPE                 "type"                     // 类型字段

// 数据类型标识
#define DATA_TYPE_SENSOR                "sensor"                   // 传感器数据
#define DATA_TYPE_EMOTION               "emotion"                  // 表情数据
#define DATA_TYPE_POSTURE               "posture"                  // 坐姿数据
#define DATA_TYPE_TEMPERATURE           "temperature"              // 温度数据
#define DATA_TYPE_HUMIDITY              "humidity"                 // 湿度数据
#define DATA_TYPE_GENERAL               "general"                  // 一般数据

// ----------------------------------------------------------------------------
// 版本信息
// ----------------------------------------------------------------------------
#define FIRMWARE_VERSION_MAJOR          1                          // 主版本号
#define FIRMWARE_VERSION_MINOR          0                          // 次版本号
#define FIRMWARE_VERSION_PATCH          0                          // 补丁版本号
#define FIRMWARE_BUILD_DATE             __DATE__                   // 构建日期
#define FIRMWARE_BUILD_TIME             __TIME__                   // 构建时间

// 版本字符串
#define FIRMWARE_VERSION_STRING         "1.0.0"
#define FIRMWARE_FULL_VERSION           "ESP32-S3 BLE Bridge v1.0.0"

// ----------------------------------------------------------------------------
// 编译时检查
// ----------------------------------------------------------------------------
#if WIFI_DATA_BUFFER_SIZE > 1024
    #error "WiFi data buffer size too large"
#endif

#if BLE_MAX_DATA_LENGTH > BLE_MTU_SIZE
    #error "BLE max data length exceeds MTU size"
#endif

#if DATA_QUEUE_SIZE > 50
    #warning "Large data queue size may cause memory issues"
#endif

#endif // BLE_BRIDGE_CONFIG_H
