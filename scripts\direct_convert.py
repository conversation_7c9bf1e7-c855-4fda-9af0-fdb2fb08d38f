#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接转换方案
将MP3文件直接复制为P3格式进行测试
"""

import shutil
from pathlib import Path

def direct_convert():
    """
    直接转换MP3为P3
    """
    print("🎵 直接转换MP3为P3格式...")
    
    temp_dir = Path("temp_voice")
    target_dir = Path("main/assets/zh-CN")
    
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 文件映射
    file_mapping = {
        "posture_bad.mp3": "health_posture_bad.p3",
        "emotion_sad.mp3": "health_emotion_sad.p3",
        "emotion_angry.mp3": "health_emotion_angry.p3",
        "emotion_happy.mp3": "health_emotion_happy.p3"
    }
    
    success_count = 0
    total_count = len(file_mapping)
    
    for mp3_name, p3_name in file_mapping.items():
        mp3_file = temp_dir / mp3_name
        p3_file = target_dir / p3_name
        
        if mp3_file.exists():
            try:
                shutil.copy2(mp3_file, p3_file)
                size = p3_file.stat().st_size
                print(f"✅ {mp3_name} -> {p3_name} ({size} bytes)")
                success_count += 1
            except Exception as e:
                print(f"❌ 转换失败 {mp3_name}: {e}")
        else:
            print(f"❌ 源文件不存在: {mp3_name}")
    
    print(f"\n📊 转换结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("\n✅ 所有文件转换成功！")
        print("\n💡 说明:")
        print("- 这是临时方案，将MP3直接作为P3使用")
        print("- 可能音质不是最优，但应该能播放")
        print("- 如果效果不好，后续可以安装专业工具优化")
        
        print("\n🚀 下一步:")
        print("1. 编译项目: idf.py build")
        print("2. 烧录设备: idf.py flash")
        print("3. 测试健康警报效果")
        
        return True
    else:
        print("\n❌ 部分文件转换失败")
        return False

def verify_files():
    """
    验证文件
    """
    print("\n🔍 验证转换后的文件...")
    
    target_dir = Path("main/assets/zh-CN")
    
    files_to_check = [
        "health_posture_bad.p3",
        "health_emotion_sad.p3", 
        "health_emotion_angry.p3",
        "health_emotion_happy.p3"
    ]
    
    all_exist = True
    for filename in files_to_check:
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {filename} ({size} bytes)")
        else:
            print(f"❌ {filename} (不存在)")
            all_exist = False
    
    return all_exist

if __name__ == "__main__":
    print("🔧 直接转换方案")
    print("=" * 30)
    
    if direct_convert():
        if verify_files():
            print("\n🎉 准备就绪！")
            print("现在可以编译和测试了！")
        else:
            print("\n⚠️ 文件验证失败")
    else:
        print("\n❌ 转换失败")
