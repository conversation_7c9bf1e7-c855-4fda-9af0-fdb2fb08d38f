#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简单的P3格式文件
使用现有的正确音效文件作为模板
"""

import os
import shutil
from pathlib import Path

def get_health_voice_mapping():
    """
    获取健康语音文件映射
    使用现有的音效文件作为模板，确保格式正确
    """
    mapping = {
        # 使用现有的音效文件作为模板
        "health_posture_bad": "break_reminder.p3",      # 休息提醒 -> 坐姿不良
        "health_posture_good": "welcome.p3",            # 欢迎音 -> 坐姿良好  
        "health_emotion_sad": "emotion_care.p3",        # 情绪关怀 -> 悲伤关怀
        "health_emotion_angry": "emotion_care.p3",      # 情绪关怀 -> 愤怒安抚
        "health_emotion_happy": "activation.p3",        # 激活音 -> 开心确认
        "health_gentle_reminder": "break_reminder.p3",  # 休息提醒 -> 温和提醒
        "health_success_confirm": "welcome.p3",         # 欢迎音 -> 成功确认
        "health_break_reminder": "break_reminder.p3",   # 休息提醒 -> 休息提醒
        "health_eye_care": "eye_care.p3"                # 眼部护理 -> 眼部护理
    }
    
    return mapping

def create_p3_files_from_templates():
    """
    从模板创建P3文件
    """
    print("🎵 从现有音效创建健康提醒P3文件...")
    
    source_dir = Path("main/assets/zh-CN")
    mapping = get_health_voice_mapping()
    
    if not source_dir.exists():
        print(f"❌ 源目录不存在: {source_dir}")
        return False
    
    success_count = 0
    total_count = len(mapping)
    
    for target_name, source_name in mapping.items():
        source_file = source_dir / source_name
        target_file = source_dir / f"{target_name}.p3"
        
        if source_file.exists():
            try:
                shutil.copy2(source_file, target_file)
                size = target_file.stat().st_size
                print(f"✅ {target_name}.p3 <- {source_name} ({size} bytes)")
                success_count += 1
            except Exception as e:
                print(f"❌ 创建失败 {target_name}: {e}")
        else:
            print(f"⚠️ 源文件不存在: {source_name}")
    
    print(f"\n📊 创建结果: {success_count}/{total_count} 成功")
    return success_count > 0

def verify_p3_files():
    """
    验证P3文件
    """
    print("\n🔍 验证P3文件...")
    
    target_dir = Path("main/assets/zh-CN")
    expected_files = [
        "health_posture_bad.p3",
        "health_posture_good.p3", 
        "health_emotion_sad.p3",
        "health_emotion_angry.p3",
        "health_emotion_happy.p3",
        "health_gentle_reminder.p3",
        "health_success_confirm.p3",
        "health_break_reminder.p3",
        "health_eye_care.p3"
    ]
    
    existing_count = 0
    for filename in expected_files:
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            if size > 0:
                print(f"✅ {filename} ({size} bytes)")
                existing_count += 1
            else:
                print(f"⚠️ {filename} (文件为空)")
        else:
            print(f"❌ {filename} (不存在)")
    
    print(f"\n📊 验证结果: {existing_count}/{len(expected_files)} 文件正常")
    return existing_count == len(expected_files)

def check_lang_config():
    """
    检查语言配置是否正确
    """
    print("\n🔍 检查语言配置...")
    
    config_file = Path("main/assets/lang_config.h")
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键常量
    required_constants = [
        "P3_HEALTH_POSTURE_BAD",
        "P3_HEALTH_POSTURE_GOOD",
        "P3_HEALTH_EMOTION_SAD", 
        "P3_HEALTH_EMOTION_ANGRY",
        "P3_HEALTH_EMOTION_HAPPY",
        "P3_HEALTH_GENTLE_REMINDER",
        "P3_HEALTH_SUCCESS_CONFIRM"
    ]
    
    missing_constants = []
    for constant in required_constants:
        if constant not in content:
            missing_constants.append(constant)
        else:
            print(f"✅ {constant}")
    
    if missing_constants:
        print(f"❌ 缺少常量: {', '.join(missing_constants)}")
        return False
    
    print("✅ 语言配置检查通过")
    return True

def create_test_script():
    """
    创建测试脚本
    """
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试健康语音文件
"""

from pathlib import Path

def test_health_voice_files():
    """
    测试健康语音文件是否正确创建
    """
    print("🧪 测试健康语音文件...")
    
    target_dir = Path("main/assets/zh-CN")
    
    test_files = {
        "health_posture_bad.p3": "坐姿不良提醒",
        "health_posture_good.p3": "坐姿良好确认",
        "health_emotion_sad.p3": "悲伤情绪关怀", 
        "health_emotion_angry.p3": "愤怒情绪安抚",
        "health_emotion_happy.p3": "开心情绪确认",
        "health_gentle_reminder.p3": "温和健康提醒",
        "health_success_confirm.p3": "健康成功确认"
    }
    
    success_count = 0
    for filename, description in test_files.items():
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            if size > 100:  # 至少100字节
                print(f"✅ {filename}: {description} ({size} bytes)")
                success_count += 1
            else:
                print(f"⚠️ {filename}: {description} (文件太小: {size} bytes)")
        else:
            print(f"❌ {filename}: {description} (不存在)")
    
    print(f"\\n📊 测试结果: {success_count}/{len(test_files)} 通过")
    
    if success_count == len(test_files):
        print("🎉 所有健康语音文件测试通过！")
        print("\\n🚀 下一步:")
        print("1. 编译项目: idf.py build")
        print("2. 烧录设备: idf.py flash") 
        print("3. 测试健康警报功能")
        print("4. 观察是否播放正确的音效")
        return True
    else:
        print("❌ 部分文件测试失败")
        return False

if __name__ == "__main__":
    test_health_voice_files()
'''
    
    with open("scripts/test_health_voice.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("✅ 创建测试脚本: scripts/test_health_voice.py")

def main():
    """
    主函数
    """
    print("🔧 创建简单P3健康语音文件")
    print("=" * 50)
    
    print("💡 说明:")
    print("- 使用现有音效文件作为模板")
    print("- 确保P3格式正确，避免音频转码问题")
    print("- 虽然内容不是真实语音，但可以验证系统工作")
    print("- 后续可以录制真实语音替换")
    
    # 创建P3文件
    if create_p3_files_from_templates():
        print("\\n✅ P3文件创建成功")
        
        # 验证文件
        if verify_p3_files():
            print("\\n✅ 文件验证通过")
            
            # 检查配置
            if check_lang_config():
                print("\\n✅ 配置检查通过")
                
                # 创建测试脚本
                create_test_script()
                
                print("\\n🎯 完成！现在可以:")
                print("1. 运行测试: python scripts/test_health_voice.py")
                print("2. 编译项目: idf.py build")
                print("3. 烧录测试: idf.py flash")
                print("4. 测试健康警报，应该能听到正确的音效")
                
                print("\\n🎵 音效映射:")
                mapping = get_health_voice_mapping()
                for target, source in mapping.items():
                    print(f"  {target} <- {source}")
                
            else:
                print("\\n❌ 配置检查失败，请检查 lang_config.h")
        else:
            print("\\n❌ 文件验证失败")
    else:
        print("\\n❌ P3文件创建失败")

if __name__ == "__main__":
    main()
