#pragma once

// 语音清晰度优化配置
namespace VoiceConfig {
    
    // TTS语音参数配置
    struct TtsParams {
        float speed;    // 语速 (0.5-2.0, 默认0.7为较慢语速)
        float pitch;    // 音调 (0.5-2.0, 默认0.9为稍低音调)
        float volume;   // 音量 (0.1-1.0, 默认1.0为正常音量)
    };
    
    // 预设语音配置
    namespace Presets {
        // 清晰模式 - 慢速、低音调，适合听不清楚时使用
        constexpr TtsParams CLEAR_MODE = {0.6f, 0.8f, 1.0f};
        
        // 标准模式 - 正常语速，平衡清晰度和自然度
        constexpr TtsParams STANDARD_MODE = {0.8f, 1.0f, 1.0f};
        
        // 快速模式 - 较快语速，适合熟悉内容时使用
        constexpr TtsParams FAST_MODE = {1.2f, 1.1f, 1.0f};
        
        // 健康提醒专用 - 慢速、稍低音调，确保重要信息清晰传达
        constexpr TtsParams HEALTH_ALERT = {0.7f, 0.9f, 1.0f};
        
        // 情绪关怀专用 - 温和语速、温暖音调
        constexpr TtsParams EMOTION_CARE = {0.75f, 0.95f, 0.9f};
    }
    
    // 根据消息类型获取最佳语音参数
    inline TtsParams GetOptimalParams(const std::string& message_type) {
        if (message_type == "health_alert") {
            return Presets::HEALTH_ALERT;
        } else if (message_type == "emotion_care") {
            return Presets::EMOTION_CARE;
        } else {
            return Presets::STANDARD_MODE;
        }
    }
    
    // 根据消息内容智能选择语音参数
    inline TtsParams GetSmartParams(const std::string& text) {
        // 检查是否包含健康相关关键词
        if (text.find("坐姿") != std::string::npos || 
            text.find("姿势") != std::string::npos ||
            text.find("挺直") != std::string::npos) {
            return Presets::HEALTH_ALERT;
        }
        
        // 检查是否包含情绪关怀关键词
        if (text.find("难过") != std::string::npos || 
            text.find("生气") != std::string::npos ||
            text.find("聊聊") != std::string::npos ||
            text.find("放松") != std::string::npos) {
            return Presets::EMOTION_CARE;
        }
        
        // 检查是否是长句子，长句子使用慢速
        if (text.length() > 20) {
            return Presets::CLEAR_MODE;
        }
        
        return Presets::STANDARD_MODE;
    }
}
