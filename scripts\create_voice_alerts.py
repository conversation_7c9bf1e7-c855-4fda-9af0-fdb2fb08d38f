#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建语音警报P3文件的脚本
将文本转换为语音，然后转换为P3格式供小智使用
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

# 添加p3_tools到路径
sys.path.append(str(Path(__file__).parent / "p3_tools"))
from convert_audio_to_p3 import encode_audio_to_opus

def text_to_speech_edge_tts(text, output_file, voice="zh-CN-XiaozhenNeural"):
    """
    使用edge-tts将文本转换为语音
    """
    try:
        cmd = [
            "edge-tts",
            "--voice", voice,
            "--text", text,
            "--write-media", output_file
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ TTS转换失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ 未找到edge-tts，请先安装: pip install edge-tts")
        return False

def create_voice_alert_p3(text, output_p3_file, voice="zh-CN-XiaozhenNeural"):
    """
    创建语音警报P3文件
    """
    print(f"🎤 正在生成语音: {text}")
    
    # 创建临时音频文件
    with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio:
        temp_audio_path = temp_audio.name
    
    try:
        # 第一步：文本转语音
        if not text_to_speech_edge_tts(text, temp_audio_path, voice):
            return False
        
        print(f"✅ 语音生成完成: {temp_audio_path}")
        
        # 第二步：转换为P3格式
        print(f"🔄 正在转换为P3格式...")
        encode_audio_to_opus(temp_audio_path, output_p3_file, target_lufs=-16.0)
        
        print(f"✅ P3文件生成完成: {output_p3_file}")
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_audio_path):
            os.unlink(temp_audio_path)

def main():
    """主函数"""
    # 语音警报文本
    voice_alerts = {
        "posture_alert.p3": "我注意到你的坐姿不太好，请调整一下坐姿，保护你的脊椎健康。",
        "emotion_sad_alert.p3": "我注意到你最近看起来有些难过，要不要聊聊天？",
        "emotion_angry_alert.p3": "我感觉到你有些生气，深呼吸一下，让我们一起放松一下。",
        "emotion_stressed_alert.p3": "你看起来有些紧张，记得适当休息，劳逸结合很重要。",
        "general_alert.p3": "小智提醒：请注意你的健康状态。",
        "break_reminder.p3": "你已经工作很久了，建议休息一下，活动活动身体。"
    }
    
    # 创建输出目录
    output_dir = Path("voice_alerts")
    output_dir.mkdir(exist_ok=True)
    
    print("🎯 开始生成语音警报文件...")
    
    success_count = 0
    for filename, text in voice_alerts.items():
        output_path = output_dir / filename
        print(f"\n📝 处理: {filename}")
        
        if create_voice_alert_p3(text, str(output_path)):
            success_count += 1
        else:
            print(f"❌ 生成失败: {filename}")
    
    print(f"\n🎉 完成！成功生成 {success_count}/{len(voice_alerts)} 个语音文件")
    print(f"📁 文件保存在: {output_dir.absolute()}")
    
    # 显示生成的文件
    print("\n📋 生成的文件列表:")
    for p3_file in output_dir.glob("*.p3"):
        size_kb = p3_file.stat().st_size / 1024
        print(f"  - {p3_file.name} ({size_kb:.1f} KB)")

if __name__ == "__main__":
    main()
