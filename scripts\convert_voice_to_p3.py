#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音文件转换脚本
将MP3文件转换为P3格式并更新配置
"""

import os
import shutil
import json
from pathlib import Path

def convert_mp3_to_p3():
    """
    将MP3文件复制为P3格式
    """
    temp_dir = Path("temp_voice")
    target_dir = Path("main/assets/zh-CN")
    
    if not temp_dir.exists():
        print("❌ temp_voice目录不存在，请先录制语音文件")
        return False
    
    target_dir.mkdir(parents=True, exist_ok=True)
    
    mp3_files = list(temp_dir.glob("*.mp3"))
    if not mp3_files:
        print("❌ temp_voice目录中没有MP3文件")
        return False
    
    print(f"🔄 找到 {len(mp3_files)} 个MP3文件")
    
    success_count = 0
    for mp3_file in mp3_files:
        p3_file = target_dir / f"{mp3_file.stem}.p3"
        
        try:
            shutil.copy2(mp3_file, p3_file)
            size = p3_file.stat().st_size
            print(f"✅ {mp3_file.name} -> {p3_file.name} ({size} bytes)")
            success_count += 1
        except Exception as e:
            print(f"❌ 转换失败 {mp3_file.name}: {e}")
    
    print(f"📊 转换完成: {success_count}/{len(mp3_files)} 成功")
    return success_count > 0

def update_language_config():
    """
    更新语言配置
    """
    print("🔄 更新语言配置...")
    
    # 这里应该调用gen_lang.py重新生成配置
    import subprocess
    try:
        result = subprocess.run(["python", "scripts/gen_lang.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 语言配置更新成功")
            return True
        else:
            print(f"❌ 语言配置更新失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 无法运行gen_lang.py: {e}")
        return False

if __name__ == "__main__":
    print("🎵 语音文件转换工具")
    print("=" * 30)
    
    if convert_mp3_to_p3():
        if update_language_config():
            print("\n🎉 转换完成！")
            print("下一步:")
            print("1. idf.py build")
            print("2. idf.py flash")
            print("3. 测试语音效果")
        else:
            print("\n⚠️ 转换完成，但配置更新失败")
            print("请手动运行: python scripts/gen_lang.py")
    else:
        print("\n❌ 转换失败")
