
        // 健康提醒预录制语音
        extern const char p3_health_posture_bad_start[] asm("_binary_health_posture_bad_p3_start");
        extern const char p3_health_posture_bad_end[] asm("_binary_health_posture_bad_p3_end");
        static const std::string_view P3_HEALTH_POSTURE_BAD {
        static_cast<const char*>(p3_health_posture_bad_start),
        static_cast<size_t>(p3_health_posture_bad_end - p3_health_posture_bad_start)
        };

        extern const char p3_health_posture_good_start[] asm("_binary_health_posture_good_p3_start");
        extern const char p3_health_posture_good_end[] asm("_binary_health_posture_good_p3_end");
        static const std::string_view P3_HEALTH_POSTURE_GOOD {
        static_cast<const char*>(p3_health_posture_good_start),
        static_cast<size_t>(p3_health_posture_good_end - p3_health_posture_good_start)
        };

        extern const char p3_health_emotion_sad_start[] asm("_binary_health_emotion_sad_p3_start");
        extern const char p3_health_emotion_sad_end[] asm("_binary_health_emotion_sad_p3_end");
        static const std::string_view P3_HEALTH_EMOTION_SAD {
        static_cast<const char*>(p3_health_emotion_sad_start),
        static_cast<size_t>(p3_health_emotion_sad_end - p3_health_emotion_sad_start)
        };

        extern const char p3_health_emotion_angry_start[] asm("_binary_health_emotion_angry_p3_start");
        extern const char p3_health_emotion_angry_end[] asm("_binary_health_emotion_angry_p3_end");
        static const std::string_view P3_HEALTH_EMOTION_ANGRY {
        static_cast<const char*>(p3_health_emotion_angry_start),
        static_cast<size_t>(p3_health_emotion_angry_end - p3_health_emotion_angry_start)
        };

        extern const char p3_health_emotion_happy_start[] asm("_binary_health_emotion_happy_p3_start");
        extern const char p3_health_emotion_happy_end[] asm("_binary_health_emotion_happy_p3_end");
        static const std::string_view P3_HEALTH_EMOTION_HAPPY {
        static_cast<const char*>(p3_health_emotion_happy_start),
        static_cast<size_t>(p3_health_emotion_happy_end - p3_health_emotion_happy_start)
        };

        extern const char p3_health_gentle_reminder_start[] asm("_binary_health_gentle_reminder_p3_start");
        extern const char p3_health_gentle_reminder_end[] asm("_binary_health_gentle_reminder_p3_end");
        static const std::string_view P3_HEALTH_GENTLE_REMINDER {
        static_cast<const char*>(p3_health_gentle_reminder_start),
        static_cast<size_t>(p3_health_gentle_reminder_end - p3_health_gentle_reminder_start)
        };

        extern const char p3_health_success_confirm_start[] asm("_binary_health_success_confirm_p3_start");
        extern const char p3_health_success_confirm_end[] asm("_binary_health_success_confirm_p3_end");
        static const std::string_view P3_HEALTH_SUCCESS_CONFIRM {
        static_cast<const char*>(p3_health_success_confirm_start),
        static_cast<size_t>(p3_health_success_confirm_end - p3_health_success_confirm_start)
        };
