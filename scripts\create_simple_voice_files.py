#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版语音文件创建工具
使用系统TTS或在线TTS服务创建语音文件
"""

import os
import json
import shutil
from pathlib import Path

def create_health_messages():
    """
    创建健康提醒消息列表
    """
    messages = {
        "health_posture_bad": {
            "text": "我发现你的坐姿不太好，记得挺直腰背哦！",
            "description": "坐姿不良提醒",
            "priority": "high"
        },
        "health_posture_good": {
            "text": "你的坐姿很端正，继续保持！",
            "description": "坐姿良好确认",
            "priority": "normal"
        },
        "health_emotion_sad": {
            "text": "我注意到你最近看起来有些难过，要不要聊聊天？",
            "description": "悲伤情绪关怀",
            "priority": "high"
        },
        "health_emotion_angry": {
            "text": "你看起来有点生气，深呼吸一下，放松心情吧。",
            "description": "愤怒情绪安抚",
            "priority": "high"
        },
        "health_emotion_happy": {
            "text": "你看起来心情很好呢，真棒！",
            "description": "开心情绪确认",
            "priority": "normal"
        },
        "health_break_reminder": {
            "text": "你已经工作很久了，该休息一下了。",
            "description": "休息提醒",
            "priority": "normal"
        },
        "health_eye_care": {
            "text": "记得让眼睛休息一下，看看远处的风景吧。",
            "description": "眼部护理提醒",
            "priority": "normal"
        },
        "health_gentle_reminder": {
            "text": "小智提醒您注意健康哦。",
            "description": "温和健康提醒",
            "priority": "low"
        },
        "health_success_confirm": {
            "text": "很好，继续保持健康的生活习惯！",
            "description": "健康成功确认",
            "priority": "normal"
        },
        "health_alert_start": {
            "text": "健康提醒",
            "description": "健康警报开始音",
            "priority": "high"
        }
    }
    
    return messages

def create_voice_instructions():
    """
    创建语音录制指导
    """
    print("🎤 小智健康提醒语音录制指导")
    print("=" * 50)
    
    messages = create_health_messages()
    
    print("📝 需要录制的语音内容:")
    print()
    
    for i, (key, msg) in enumerate(messages.items(), 1):
        priority_icon = {"high": "🔴", "normal": "🟡", "low": "🟢"}[msg["priority"]]
        print(f"{i:2d}. {priority_icon} {msg['description']}")
        print(f"    文件名: {key}.mp3")
        print(f"    内容: \"{msg['text']}\"")
        print(f"    建议: ", end="")
        
        if msg["priority"] == "high":
            print("慢速、清晰、稍低音调")
        elif msg["priority"] == "normal":
            print("正常语速、温和音调")
        else:
            print("轻柔语速、温暖音调")
        print()
    
    return messages

def create_recording_script():
    """
    创建录制脚本
    """
    messages = create_health_messages()
    
    script_content = """# 小智健康提醒语音录制脚本

## 录制要求
- 采样率: 16kHz
- 声道: 单声道 (Mono)
- 格式: MP3
- 比特率: 32-64 kbps
- 语速: 根据优先级调整

## 录制建议
- 🔴 高优先级: 慢速(0.7x)、清晰、稍低音调，确保用户能听清楚
- 🟡 普通优先级: 正常语速(1.0x)、温和音调
- 🟢 低优先级: 轻柔语速(0.8x)、温暖音调

## 录制内容

"""
    
    for i, (key, msg) in enumerate(messages.items(), 1):
        priority_icon = {"high": "🔴", "normal": "🟡", "low": "🟢"}[msg["priority"]]
        script_content += f"""### {i}. {msg['description']} {priority_icon}
**文件名**: `{key}.mp3`
**内容**: "{msg['text']}"
**优先级**: {msg['priority']}

"""
    
    script_content += """
## 录制后处理
1. 将所有MP3文件放在 `temp_voice/` 目录
2. 运行转换脚本: `python scripts/convert_voice_to_p3.py`
3. 重新编译项目: `idf.py build`
4. 烧录测试: `idf.py flash`

## 质量检查
- 每个文件大小应在 1-10KB 之间
- 语音清晰，无杂音
- 语速适中，易于理解
- 音调自然，符合场景
"""
    
    return script_content

def create_conversion_script():
    """
    创建转换脚本
    """
    conversion_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音文件转换脚本
将MP3文件转换为P3格式并更新配置
"""

import os
import shutil
import json
from pathlib import Path

def convert_mp3_to_p3():
    """
    将MP3文件复制为P3格式
    """
    temp_dir = Path("temp_voice")
    target_dir = Path("main/assets/zh-CN")
    
    if not temp_dir.exists():
        print("❌ temp_voice目录不存在，请先录制语音文件")
        return False
    
    target_dir.mkdir(parents=True, exist_ok=True)
    
    mp3_files = list(temp_dir.glob("*.mp3"))
    if not mp3_files:
        print("❌ temp_voice目录中没有MP3文件")
        return False
    
    print(f"🔄 找到 {len(mp3_files)} 个MP3文件")
    
    success_count = 0
    for mp3_file in mp3_files:
        p3_file = target_dir / f"{mp3_file.stem}.p3"
        
        try:
            shutil.copy2(mp3_file, p3_file)
            size = p3_file.stat().st_size
            print(f"✅ {mp3_file.name} -> {p3_file.name} ({size} bytes)")
            success_count += 1
        except Exception as e:
            print(f"❌ 转换失败 {mp3_file.name}: {e}")
    
    print(f"📊 转换完成: {success_count}/{len(mp3_files)} 成功")
    return success_count > 0

def update_language_config():
    """
    更新语言配置
    """
    print("🔄 更新语言配置...")
    
    # 这里应该调用gen_lang.py重新生成配置
    import subprocess
    try:
        result = subprocess.run(["python", "scripts/gen_lang.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 语言配置更新成功")
            return True
        else:
            print(f"❌ 语言配置更新失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 无法运行gen_lang.py: {e}")
        return False

if __name__ == "__main__":
    print("🎵 语音文件转换工具")
    print("=" * 30)
    
    if convert_mp3_to_p3():
        if update_language_config():
            print("\\n🎉 转换完成！")
            print("下一步:")
            print("1. idf.py build")
            print("2. idf.py flash")
            print("3. 测试语音效果")
        else:
            print("\\n⚠️ 转换完成，但配置更新失败")
            print("请手动运行: python scripts/gen_lang.py")
    else:
        print("\\n❌ 转换失败")
'''
    
    return conversion_script

def main():
    """
    主函数
    """
    print("🎤 小智健康提醒语音文件创建工具")
    print("=" * 50)
    
    # 创建录制指导
    messages = create_voice_instructions()
    
    # 创建目录
    temp_dir = Path("temp_voice")
    scripts_dir = Path("scripts")
    
    temp_dir.mkdir(exist_ok=True)
    scripts_dir.mkdir(exist_ok=True)
    
    # 创建录制脚本文件
    script_content = create_recording_script()
    with open("VOICE_RECORDING_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ 创建录制指导文件: VOICE_RECORDING_GUIDE.md")
    
    # 创建转换脚本
    conversion_script = create_conversion_script()
    with open("scripts/convert_voice_to_p3.py", "w", encoding="utf-8") as f:
        f.write(conversion_script)
    
    print("✅ 创建转换脚本: scripts/convert_voice_to_p3.py")
    
    # 创建消息配置文件
    with open("temp_voice/messages.json", "w", encoding="utf-8") as f:
        json.dump(messages, f, ensure_ascii=False, indent=2)
    
    print("✅ 创建消息配置: temp_voice/messages.json")
    
    print("\\n🎯 下一步操作:")
    print("1. 查看录制指导: VOICE_RECORDING_GUIDE.md")
    print("2. 录制语音文件到 temp_voice/ 目录")
    print("3. 运行转换: python scripts/convert_voice_to_p3.py")
    print("4. 编译测试: idf.py build && idf.py flash")
    
    print("\\n💡 录制建议:")
    print("- 使用手机录音应用或电脑录音软件")
    print("- 保持安静环境，避免杂音")
    print("- 语速要慢，发音要清晰")
    print("- 音调要自然，符合小智的温和形象")

if __name__ == "__main__":
    main()
