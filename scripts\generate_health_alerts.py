#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康提醒语音生成脚本
生成健康提醒的MP3文件，然后转换为P3格式
"""

import os
import sys
import subprocess
from pathlib import Path
import requests
import json
import time

# 健康提醒话语
HEALTH_ALERTS = {
    # 姿势提醒
    "posture_alert_1": "请注意您的坐姿，保持背部挺直",
    "posture_alert_2": "您已经保持同一姿势很久了，建议调整一下坐姿",
    "posture_alert_3": "为了您的健康，请挺直腰背，放松肩膀",
    "posture_alert_4": "检测到不良坐姿，请调整您的姿势",
    "posture_alert_5": "长时间弯腰对脊椎不好，请坐直一些",
    
    # 休息提醒
    "break_alert_1": "您已经工作很久了，建议休息一下",
    "break_alert_2": "该起来活动活动了，适当休息有益健康",
    "break_alert_3": "工作重要，健康更重要，请适当休息",
    "break_alert_4": "建议您站起来走动几分钟，缓解疲劳",
    "break_alert_5": "长时间工作容易疲劳，请注意劳逸结合",
    
    # 眼部护理提醒
    "eye_care_1": "请注意保护眼睛，适当远眺放松",
    "eye_care_2": "长时间用眼容易疲劳，建议眨眨眼睛",
    "eye_care_3": "为了保护视力，请看看远处的景物",
    "eye_care_4": "眼睛是心灵的窗户，请注意用眼卫生",
    "eye_care_5": "建议您闭眼休息几秒钟，缓解眼部疲劳",
    
    # 情绪关怀
    "emotion_care_1": "检测到您可能有些疲惫，请注意休息",
    "emotion_care_2": "保持良好的心情对健康很重要",
    "emotion_care_3": "工作之余，别忘了关爱自己的身心健康",
    "emotion_care_4": "适当的放松能让您更有效率",
    "emotion_care_5": "您的健康是最重要的，请注意劳逸结合",
    
    # 通用健康提醒
    "general_health_1": "健康提醒：请注意保持良好的工作习惯",
    "general_health_2": "小智提醒您：健康工作，快乐生活",
    "general_health_3": "温馨提示：适当的休息能提高工作效率",
    "general_health_4": "健康小贴士：保持正确的坐姿很重要",
    "general_health_5": "小智关心您的健康，请注意劳逸结合",
    
    # 鼓励话语
    "encouragement_1": "您今天的坐姿很标准，继续保持",
    "encouragement_2": "很好，您的工作习惯很健康",
    "encouragement_3": "您的健康意识很强，值得表扬",
    "encouragement_4": "坚持良好的习惯，您会更健康",
    "encouragement_5": "您做得很好，继续保持健康的生活方式",
}

def generate_tts_audio(text, output_file, voice="zh-CN-XiaoxiaoNeural"):
    """
    使用edge-tts生成语音文件
    """
    try:
        # 使用edge-tts命令行工具
        cmd = [
            "edge-tts",
            "--voice", voice,
            "--text", text,
            "--write-media", output_file
        ]
        
        print(f"生成语音: {text}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"TTS生成失败: {result.stderr}")
            return False
            
        return True
        
    except FileNotFoundError:
        print("错误: 未找到edge-tts命令，请先安装: pip install edge-tts")
        return False
    except Exception as e:
        print(f"TTS生成错误: {e}")
        return False

def convert_mp3_to_p3(mp3_file, p3_file):
    """
    将MP3文件转换为P3格式
    """
    try:
        # 获取脚本目录
        script_dir = Path(__file__).parent
        convert_script = script_dir / "p3_tools" / "convert_audio_to_p3.py"
        
        if not convert_script.exists():
            print(f"错误: 转换脚本不存在: {convert_script}")
            return False
        
        cmd = [
            sys.executable,
            str(convert_script),
            str(mp3_file),
            str(p3_file),
            "-d"  # 禁用响度标准化，因为TTS音频已经标准化
        ]
        
        print(f"转换为P3格式: {mp3_file} -> {p3_file}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"P3转换失败: {result.stderr}")
            return False
            
        return True
        
    except Exception as e:
        print(f"P3转换错误: {e}")
        return False

def main():
    """
    主函数：生成所有健康提醒语音文件
    """
    # 创建输出目录
    output_dir = Path("health_alerts_audio")
    mp3_dir = output_dir / "mp3"
    p3_dir = output_dir / "p3"
    
    mp3_dir.mkdir(parents=True, exist_ok=True)
    p3_dir.mkdir(parents=True, exist_ok=True)
    
    print("开始生成健康提醒语音文件...")
    print(f"输出目录: {output_dir.absolute()}")
    
    success_count = 0
    total_count = len(HEALTH_ALERTS)
    
    for filename, text in HEALTH_ALERTS.items():
        mp3_file = mp3_dir / f"{filename}.mp3"
        p3_file = p3_dir / f"{filename}.p3"
        
        # 生成MP3文件
        if generate_tts_audio(text, str(mp3_file)):
            # 转换为P3格式
            if convert_mp3_to_p3(mp3_file, p3_file):
                success_count += 1
                print(f"✅ 成功生成: {filename}")
            else:
                print(f"❌ P3转换失败: {filename}")
        else:
            print(f"❌ TTS生成失败: {filename}")
        
        # 添加小延迟避免请求过快
        time.sleep(0.5)
    
    print(f"\n生成完成！成功: {success_count}/{total_count}")
    print(f"MP3文件位置: {mp3_dir.absolute()}")
    print(f"P3文件位置: {p3_dir.absolute()}")
    
    if success_count > 0:
        print("\n下一步:")
        print("1. 将P3文件复制到 main/assets/zh-CN/ 目录")
        print("2. 运行 python scripts/gen_lang.py 更新语言配置")
        print("3. 重新编译项目")

if __name__ == "__main__":
    main()
