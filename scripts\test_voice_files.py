#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
验证语音文件是否正确创建
"""

from pathlib import Path

def test_voice_files():
    """
    测试语音文件
    """
    print("🧪 测试语音文件...")
    
    target_dir = Path("main/assets/zh-CN")
    
    expected_files = [
        "health_posture_bad.p3",
        "health_posture_good.p3", 
        "health_emotion_sad.p3",
        "health_emotion_angry.p3",
        "health_emotion_happy.p3",
        "health_gentle_reminder.p3",
        "health_success_confirm.p3"
    ]
    
    success_count = 0
    for filename in expected_files:
        file_path = target_dir / filename
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {filename} ({size} bytes)")
            success_count += 1
        else:
            print(f"❌ {filename} (不存在)")
    
    print(f"\n📊 测试结果: {success_count}/{len(expected_files)} 文件存在")
    
    if success_count == len(expected_files):
        print("🎉 所有语音文件都已创建！")
        print("\n下一步:")
        print("1. 将 temp_health_voice_constants.h 的内容添加到 lang_config.h")
        print("2. 编译项目: idf.py build")
        print("3. 烧录测试: idf.py flash")
    else:
        print("⚠️ 部分文件缺失，请检查创建过程")

if __name__ == "__main__":
    test_voice_files()
